import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { quizzes, questions, chapters, modules, courses, users } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/quizzes - Get quizzes for a chapter or course
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const chapterId = searchParams.get('chapterId');
    const courseId = searchParams.get('courseId');
    const teacherId = searchParams.get('teacherId');
    
    if (!teacherId) {
      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });
    }

    let whereCondition;
    
    if (chapterId) {
      // Get quizzes for a specific chapter
      // First verify teacher has access to this chapter
      const chapterWithCourse = await db
        .select({
          chapterId: chapters.id,
          moduleId: chapters.moduleId,
          courseId: modules.courseId,
          teacherId: courses.teacherId
        })
        .from(chapters)
        .leftJoin(modules, eq(chapters.moduleId, modules.id))
        .leftJoin(courses, eq(modules.courseId, courses.id))
        .where(
          and(
            eq(chapters.id, parseInt(chapterId)),
            eq(courses.teacherId, parseInt(teacherId))
          )
        )
        .limit(1);

      if (chapterWithCourse.length === 0) {
        return NextResponse.json(
          { error: 'Chapter not found or access denied' },
          { status: 403 }
        );
      }

      whereCondition = eq(quizzes.chapterId, parseInt(chapterId));
    } else if (courseId) {
      // Get all quizzes for a course
      const course = await db
        .select()
        .from(courses)
        .where(
          and(
            eq(courses.id, parseInt(courseId)),
            eq(courses.teacherId, parseInt(teacherId))
          )
        )
        .limit(1);

      if (course.length === 0) {
        return NextResponse.json(
          { error: 'Course not found or access denied' },
          { status: 403 }
        );
      }

      // Get all chapters for this course
      const courseModules = await db
        .select({ id: modules.id })
        .from(modules)
        .where(eq(modules.courseId, parseInt(courseId)));

      const moduleIds = courseModules.map(m => m.id);
      if (moduleIds.length === 0) {
        return NextResponse.json({ quizzes: [] });
      }

      const courseChapters = await db
        .select({ id: chapters.id })
        .from(chapters)
        .where(eq(chapters.moduleId, moduleIds[0])); // This needs to be fixed for multiple modules

      const chapterIds = courseChapters.map(c => c.id);
      if (chapterIds.length === 0) {
        return NextResponse.json({ quizzes: [] });
      }

      whereCondition = eq(quizzes.chapterId, chapterIds[0]); // This needs to be fixed
    } else {
      // Get all quizzes for teacher's courses
      const teacherCourses = await db
        .select({ id: courses.id })
        .from(courses)
        .where(eq(courses.teacherId, parseInt(teacherId)));
      
      const courseIds = teacherCourses.map(c => c.id);
      if (courseIds.length === 0) {
        return NextResponse.json({ quizzes: [] });
      }

      // This is a simplified approach - in practice, you'd want to join properly
      whereCondition = eq(quizzes.chapterId, 1); // Placeholder - needs proper implementation
    }

    // Get quizzes with chapter and course information
    const quizList = await db
      .select({
        id: quizzes.id,
        name: quizzes.name,
        description: quizzes.description,
        quizType: quizzes.quizType,
        timeLimit: quizzes.timeLimit,
        minimumScore: quizzes.minimumScore,
        isActive: quizzes.isActive,
        chapterId: quizzes.chapterId,
        createdAt: quizzes.createdAt,
        updatedAt: quizzes.updatedAt,
        chapterName: chapters.name,
        moduleName: modules.name,
        courseName: courses.name
      })
      .from(quizzes)
      .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
      .leftJoin(modules, eq(chapters.moduleId, modules.id))
      .leftJoin(courses, eq(modules.courseId, courses.id))
      .where(whereCondition);

    // Get question count for each quiz
    const quizzesWithQuestionCount = await Promise.all(
      quizList.map(async (quiz) => {
        const questionCount = await db
          .select({ count: questions.id })
          .from(questions)
          .where(eq(questions.quizId, quiz.id));

        return {
          ...quiz,
          questionCount: questionCount.length
        };
      })
    );

    return NextResponse.json({ quizzes: quizzesWithQuestionCount });
  } catch (error) {
    console.error('Error fetching quizzes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/quizzes - Create a new quiz
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      description,
      quizType = 'chapter',
      timeLimit,
      minimumScore = 70,
      isActive = true,
      chapterId,
      teacherId,
      questions: quizQuestions = []
    } = body;

    // Validate required fields
    if (!name || !chapterId || !teacherId) {
      return NextResponse.json(
        { error: 'Name, chapter ID, and teacher ID are required' },
        { status: 400 }
      );
    }

    // Verify chapter exists and teacher has access
    const chapterWithCourse = await db
      .select({
        chapterId: chapters.id,
        moduleId: chapters.moduleId,
        courseId: modules.courseId,
        teacherId: courses.teacherId
      })
      .from(chapters)
      .leftJoin(modules, eq(chapters.moduleId, modules.id))
      .leftJoin(courses, eq(modules.courseId, courses.id))
      .where(
        and(
          eq(chapters.id, chapterId),
          eq(courses.teacherId, teacherId)
        )
      )
      .limit(1);

    if (chapterWithCourse.length === 0) {
      return NextResponse.json(
        { error: 'Chapter not found or access denied' },
        { status: 403 }
      );
    }

    // Create the quiz
    const newQuiz = await db
      .insert(quizzes)
      .values({
        name,
        description,
        quizType,
        timeLimit,
        minimumScore: minimumScore.toString(),
        isActive,
        chapterId
      })
      .returning();

    const quizId = newQuiz[0].id;

    // Create questions if provided
    if (quizQuestions.length > 0) {
      const questionsToInsert = quizQuestions.map((question: any, index: number) => ({
        quizId,
        type: question.type || 'multiple_choice',
        question: question.question,
        options: question.options ? JSON.stringify(question.options) : null,
        correctAnswer: question.correctAnswer,
        explanation: question.explanation,
        points: question.points || 1,
        orderIndex: question.orderIndex || index + 1
      }));

      await db.insert(questions).values(questionsToInsert);
    }

    return NextResponse.json(
      { quiz: newQuiz[0], message: 'Quiz created successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating quiz:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}