'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { authStorage } from '@/lib/auth';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  UserCheck,
  Search,
  MoreHorizontal,
  Eye,
  UserX,
  Copy,
  QrCode
} from 'lucide-react';

interface EnrollmentRequest {
  id: number;
  studentName: string;
  studentEmail: string;
  courseName: string;
  courseCode: string;
  requestDate: string;
  status: string;
}

interface ActiveEnrollment {
  id: number;
  studentName: string;
  studentEmail: string;
  courseName: string;
  courseCode: string;
  enrolledDate: string;
  progress: number;
  status: string;
}

interface CourseCode {
  code: string;
  name: string;
  students: number;
}

export default function EnrollmentsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [enrollmentRequests, setEnrollmentRequests] = useState<EnrollmentRequest[]>([]);
  const [activeEnrollments, setActiveEnrollments] = useState<ActiveEnrollment[]>([]);
  const [courseCodes, setCourseCodes] = useState<CourseCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [processingRequest, setProcessingRequest] = useState<number | null>(null);

  useEffect(() => {
    fetchEnrollmentData();
  }, []);

  const fetchEnrollmentData = async () => {
    try {
      const user = authStorage.getUser();
      if (!user || user.role !== 'teacher') {
        toast.error('Access denied. Teacher account required.');
        return;
      }

      // Fetch enrollment requests
      const requestsResponse = await fetch(`/api/enrollments/requests?teacherId=${user.id}`);
      if (requestsResponse.ok) {
        const requestsData = await requestsResponse.json();
        setEnrollmentRequests(requestsData);
      }

      // Fetch active enrollments
      const enrollmentsResponse = await fetch(`/api/enrollments?teacherId=${user.id}`);
      if (enrollmentsResponse.ok) {
        const enrollmentsData = await enrollmentsResponse.json();
        setActiveEnrollments(enrollmentsData);
      }

      // Fetch course codes
      const coursesResponse = await fetch(`/api/courses?teacherId=${user.id}`);
      if (coursesResponse.ok) {
        const coursesData = await coursesResponse.json();
        const codes = coursesData.map((course: any) => ({
          code: course.courseCode,
          name: course.name,
          students: course.enrollmentCount || 0
        }));
        setCourseCodes(codes);
      }
    } catch (error) {
      console.error('Error fetching enrollment data:', error);
      toast.error('Failed to load enrollment data');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const handleApproveEnrollment = async (id: number) => {
    const user = authStorage.getUser();
    if (!user || user.role !== 'teacher') {
      toast.error('Access denied');
      return;
    }

    setProcessingRequest(id);
    try {
      const response = await fetch(`/api/enrollments/requests/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'approve',
          teacherId: user.id
        }),
      });

      if (response.ok) {
        toast.success('Enrollment request approved');
        fetchEnrollmentData(); // Refresh data
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to approve enrollment');
      }
    } catch (error) {
      console.error('Error approving enrollment:', error);
      toast.error('An error occurred while approving enrollment');
    } finally {
      setProcessingRequest(null);
    }
  };

  const handleRejectEnrollment = async (id: number) => {
    const user = authStorage.getUser();
    if (!user || user.role !== 'teacher') {
      toast.error('Access denied');
      return;
    }

    setProcessingRequest(id);
    try {
      const response = await fetch(`/api/enrollments/requests/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'reject',
          teacherId: user.id
        }),
      });

      if (response.ok) {
        toast.success('Enrollment request rejected');
        fetchEnrollmentData(); // Refresh data
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to reject enrollment');
      }
    } catch (error) {
      console.error('Error rejecting enrollment:', error);
      toast.error('An error occurred while rejecting enrollment');
    } finally {
      setProcessingRequest(null);
    }
  };

  const filteredRequests = enrollmentRequests.filter(
    (request) =>
      request.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.studentEmail.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.courseName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredEnrollments = activeEnrollments.filter(
    (enrollment) =>
      enrollment.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      enrollment.studentEmail
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      enrollment.courseName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Student Enrollments
          </h1>
          <p className='text-muted-foreground'>
            Manage student enrollments and course access
          </p>
        </div>
      </div>

      <Tabs defaultValue='requests' className='space-y-6'>
        <TabsList>
          <TabsTrigger value='requests'>Enrollment Requests</TabsTrigger>
          <TabsTrigger value='active'>Active Enrollments</TabsTrigger>
          <TabsTrigger value='codes'>Course Codes</TabsTrigger>
        </TabsList>

        <TabsContent value='requests'>
          <Card>
            <CardHeader>
              <CardTitle>Pending Enrollment Requests</CardTitle>
              <CardDescription>
                Review and approve student enrollment requests
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='mb-4 flex items-center space-x-2'>
                <div className='relative flex-1'>
                  <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
                  <Input
                    placeholder='Search requests...'
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className='pl-8'
                  />
                </div>
              </div>

              {loading ? (
                <div className='rounded-md border'>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Student</TableHead>
                        <TableHead>Course</TableHead>
                        <TableHead>Request Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className='w-[100px]'>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {[1, 2, 3].map((i) => (
                        <TableRow key={i}>
                          <TableCell>
                            <div className='space-y-1'>
                              <div className='bg-muted h-4 w-32 rounded animate-pulse'></div>
                              <div className='bg-muted h-3 w-40 rounded animate-pulse'></div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className='space-y-1'>
                              <div className='bg-muted h-4 w-36 rounded animate-pulse'></div>
                              <div className='bg-muted h-3 w-20 rounded animate-pulse'></div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className='bg-muted h-4 w-24 rounded animate-pulse'></div>
                          </TableCell>
                          <TableCell>
                            <div className='bg-muted h-6 w-16 rounded animate-pulse'></div>
                          </TableCell>
                          <TableCell>
                            <div className='flex space-x-2'>
                              <div className='bg-muted h-8 w-8 rounded animate-pulse'></div>
                              <div className='bg-muted h-8 w-8 rounded animate-pulse'></div>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className='rounded-md border'>
                  <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Request Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className='w-[100px]'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRequests.map((request) => (
                      <TableRow key={request.id}>
                        <TableCell>
                          <div className='space-y-1'>
                            <p className='font-medium'>{request.studentName}</p>
                            <p className='text-muted-foreground text-sm'>
                              {request.studentEmail}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='space-y-1'>
                            <p className='font-medium'>{request.courseName}</p>
                            <code className='bg-muted rounded px-1 text-sm'>
                              {request.courseCode}
                            </code>
                          </div>
                        </TableCell>
                        <TableCell>
                          {new Date(request.requestDate).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <Badge variant='outline'>{request.status}</Badge>
                        </TableCell>
                        <TableCell>
                          <div className='flex space-x-2'>
                            <Button
                              size='sm'
                              onClick={() =>
                                handleApproveEnrollment(request.id)
                              }
                              disabled={processingRequest === request.id}
                            >
                              <UserCheck className='h-3 w-3' />
                            </Button>
                            <Button
                              size='sm'
                              variant='outline'
                              onClick={() => handleRejectEnrollment(request.id)}
                              disabled={processingRequest === request.id}
                            >
                              <UserX className='h-3 w-3' />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                  </Table>
                </div>
              )}

              {!loading && filteredRequests.length === 0 && (
                <div className='py-8 text-center'>
                  <UserCheck className='text-muted-foreground mx-auto h-12 w-12' />
                  <h3 className='mt-2 text-sm font-semibold'>
                    No pending requests
                  </h3>
                  <p className='text-muted-foreground mt-1 text-sm'>
                    All enrollment requests have been processed.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='active'>
          <Card>
            <CardHeader>
              <CardTitle>Active Enrollments</CardTitle>
              <CardDescription>
                View all active student enrollments and their progress
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='mb-4 flex items-center space-x-2'>
                <div className='relative flex-1'>
                  <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
                  <Input
                    placeholder='Search enrollments...'
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className='pl-8'
                  />
                </div>
              </div>

              {loading ? (
                <div className='rounded-md border'>
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Student</TableHead>
                        <TableHead>Course</TableHead>
                        <TableHead>Progress</TableHead>
                        <TableHead>Enrolled Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className='w-[70px]'>Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {[1, 2, 3].map((i) => (
                        <TableRow key={i}>
                          <TableCell>
                            <div className='space-y-1'>
                              <div className='bg-muted h-4 w-32 rounded animate-pulse'></div>
                              <div className='bg-muted h-3 w-40 rounded animate-pulse'></div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className='space-y-1'>
                              <div className='bg-muted h-4 w-36 rounded animate-pulse'></div>
                              <div className='bg-muted h-3 w-20 rounded animate-pulse'></div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className='bg-muted h-4 w-20 rounded animate-pulse'></div>
                          </TableCell>
                          <TableCell>
                            <div className='bg-muted h-4 w-24 rounded animate-pulse'></div>
                          </TableCell>
                          <TableCell>
                            <div className='bg-muted h-6 w-16 rounded animate-pulse'></div>
                          </TableCell>
                          <TableCell>
                            <div className='bg-muted h-8 w-8 rounded animate-pulse'></div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              ) : (
                <div className='rounded-md border'>
                  <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student</TableHead>
                      <TableHead>Course</TableHead>
                      <TableHead>Progress</TableHead>
                      <TableHead>Enrolled Date</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead className='w-[70px]'>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredEnrollments.map((enrollment) => (
                      <TableRow key={enrollment.id}>
                        <TableCell>
                          <div className='space-y-1'>
                            <p className='font-medium'>
                              {enrollment.studentName}
                            </p>
                            <p className='text-muted-foreground text-sm'>
                              {enrollment.studentEmail}
                            </p>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='space-y-1'>
                            <p className='font-medium'>
                              {enrollment.courseName}
                            </p>
                            <code className='bg-muted rounded px-1 text-sm'>
                              {enrollment.courseCode}
                            </code>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className='space-y-1'>
                            <div className='flex items-center space-x-2'>
                              <div className='bg-muted h-2 w-16 rounded-full'>
                                <div
                                  className='bg-primary h-2 rounded-full'
                                  style={{ width: `${enrollment.progress}%` }}
                                />
                              </div>
                              <span className='text-sm'>
                                {enrollment.progress}%
                              </span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          {new Date(
                            enrollment.enrolledDate
                          ).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={
                              enrollment.status === 'completed'
                                ? 'default'
                                : 'secondary'
                            }
                          >
                            {enrollment.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant='ghost' className='h-8 w-8 p-0'>
                                <MoreHorizontal className='h-4 w-4' />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align='end'>
                              <DropdownMenuItem>
                                <Eye className='mr-2 h-4 w-4' />
                                View Progress
                              </DropdownMenuItem>
                              <DropdownMenuItem className='text-red-600'>
                                <UserX className='mr-2 h-4 w-4' />
                                Remove Student
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='codes'>
          <Card>
            <CardHeader>
              <CardTitle>Course Codes</CardTitle>
              <CardDescription>
                Share these codes with students for easy enrollment
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='grid gap-4'>
                {courseCodes.map((course) => (
                  <div
                    key={course.code}
                    className='flex items-center justify-between rounded-lg border p-4'
                  >
                    <div className='space-y-1'>
                      <p className='font-medium'>{course.name}</p>
                      <p className='text-muted-foreground text-sm'>
                        {course.students} students enrolled
                      </p>
                    </div>
                    <div className='flex items-center space-x-2'>
                      <code className='bg-muted rounded px-3 py-2 font-mono text-lg'>
                        {course.code}
                      </code>
                      <Button
                        variant='outline'
                        size='sm'
                        onClick={() => copyToClipboard(course.code)}
                      >
                        <Copy className='h-4 w-4' />
                      </Button>
                      <Button variant='outline' size='sm'>
                        <QrCode className='h-4 w-4' />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
