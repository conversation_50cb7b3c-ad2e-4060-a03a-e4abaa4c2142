import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent } from '@/components/ui/card';
import { Clock } from 'lucide-react';
import { QuizModalProps } from '@/types/lms';

export const QuizModal: React.FC<QuizModalProps> = ({
  quiz,
  isOpen,
  onClose,
  onComplete
}) => {
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [answers, setAnswers] = useState<{ [key: string]: any }>({});
  const [timeLeft, setTimeLeft] = useState(
    quiz.timeLimit ? quiz.timeLimit * 60 : null
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  React.useEffect(() => {
    if (isOpen && timeLeft !== null && timeLeft > 0) {
      const timer = setInterval(() => {
        setTimeLeft((prev) => {
          if (prev === null || prev <= 1) {
            handleSubmitQuiz();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
      return () => clearInterval(timer);
    }
  }, [isOpen, timeLeft]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleAnswerChange = (questionId: string, answer: any) => {
    setAnswers((prev) => ({ ...prev, [questionId]: answer }));
  };

  const handleSubmitQuiz = () => {
    if (isSubmitting) return;
    setIsSubmitting(true);

    // Calculate score
    let correctAnswers = 0;
    quiz.questions.forEach((question) => {
      if (answers[question.id] === question.correctAnswer) {
        correctAnswers++;
      }
    });

    const score = Math.round((correctAnswers / quiz.questions.length) * 100);
    setTimeout(() => {
      onComplete(score);
      setIsSubmitting(false);
      setAnswers({});
      setCurrentQuestion(0);
      if (quiz.timeLimit) setTimeLeft(quiz.timeLimit * 60);
    }, 1000);
  };

  if (!isOpen || quiz.questions.length === 0) return null;

  const currentQ = quiz.questions[currentQuestion];
  const isLastQuestion = currentQuestion === quiz.questions.length - 1;
  const canProceed = answers[currentQ.id] !== undefined;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className='max-h-[90vh] max-w-4xl overflow-y-auto'>
        <DialogHeader>
          <DialogTitle className='flex items-center justify-between'>
            <span>{quiz.title}</span>
            {timeLeft !== null && (
              <Badge variant='outline' className='border-red-200 text-red-600'>
                <Clock className='mr-1 h-4 w-4' />
                {formatTime(timeLeft)}
              </Badge>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className='space-y-6'>
          {/* Progress Bar */}
          <div className='space-y-2'>
            <div className='flex justify-between text-sm text-gray-600'>
              <span>
                Question {currentQuestion + 1} of {quiz.questions.length}
              </span>
              <span>
                {Math.round(
                  ((currentQuestion + 1) / quiz.questions.length) * 100
                )}
                % Complete
              </span>
            </div>
            <Progress
              value={((currentQuestion + 1) / quiz.questions.length) * 100}
            />
          </div>

          {/* Question */}
          <Card>
            <CardContent className='p-6'>
              <h3 className='mb-4 text-lg font-medium'>{currentQ.question}</h3>

              {currentQ.type === 'multiple-choice' && currentQ.options && (
                <div className='space-y-3'>
                  {currentQ.options.map((option, index) => (
                    <label
                      key={index}
                      className='flex cursor-pointer items-center space-x-3'
                    >
                      <input
                        type='radio'
                        name={currentQ.id}
                        value={index}
                        checked={answers[currentQ.id] === index}
                        onChange={() => handleAnswerChange(currentQ.id, index)}
                        className='h-4 w-4 text-blue-600'
                      />
                      <span>{option}</span>
                    </label>
                  ))}
                </div>
              )}

              {currentQ.type === 'true-false' && (
                <div className='space-y-3'>
                  <label className='flex cursor-pointer items-center space-x-3'>
                    <input
                      type='radio'
                      name={currentQ.id}
                      value='true'
                      checked={answers[currentQ.id] === 'true'}
                      onChange={() => handleAnswerChange(currentQ.id, 'true')}
                      className='h-4 w-4 text-blue-600'
                    />
                    <span>True</span>
                  </label>
                  <label className='flex cursor-pointer items-center space-x-3'>
                    <input
                      type='radio'
                      name={currentQ.id}
                      value='false'
                      checked={answers[currentQ.id] === 'false'}
                      onChange={() => handleAnswerChange(currentQ.id, 'false')}
                      className='h-4 w-4 text-blue-600'
                    />
                    <span>False</span>
                  </label>
                </div>
              )}

              {currentQ.type === 'essay' && (
                <textarea
                  className='w-full resize-none rounded-lg border p-3 focus:border-transparent focus:ring-2 focus:ring-blue-500'
                  rows={6}
                  placeholder='Type your answer here...'
                  value={answers[currentQ.id] || ''}
                  onChange={(e) =>
                    handleAnswerChange(currentQ.id, e.target.value)
                  }
                />
              )}
            </CardContent>
          </Card>

          {/* Navigation */}
          <div className='flex justify-between'>
            <Button
              variant='outline'
              onClick={() =>
                setCurrentQuestion((prev) => Math.max(0, prev - 1))
              }
              disabled={currentQuestion === 0 || isSubmitting}
            >
              Previous
            </Button>

            <div className='flex space-x-2'>
              {!isLastQuestion ? (
                <Button
                  onClick={() => setCurrentQuestion((prev) => prev + 1)}
                  disabled={!canProceed || isSubmitting}
                >
                  Next
                </Button>
              ) : (
                <Button
                  onClick={handleSubmitQuiz}
                  disabled={!canProceed || isSubmitting}
                  className='bg-green-600 hover:bg-green-700'
                >
                  {isSubmitting ? 'Submitting...' : 'Submit Quiz'}
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
