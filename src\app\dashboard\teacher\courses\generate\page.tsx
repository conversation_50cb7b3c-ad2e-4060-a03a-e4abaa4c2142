'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Upload, Bot, FileText, Loader2 } from 'lucide-react';
import Link from 'next/link';
// Import optimized functions
import { generateCourse } from '@/lib/gemini';

export default function GenerateCoursePage() {
  const router = useRouter();
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [streamingText, setStreamingText] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [courseOutline, setCourseOutline] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState({
    courseName: '',
    courseDescription: '',
    courseType: 'self_paced',
    targetAudience: '',
    difficulty: 'beginner'
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      setSelectedFile(file);
      setError(null);
    } else {
      setError('Please select a PDF file');
    }
  };

  const handleGenerateOutline = async () => {
    if (!selectedFile) {
      setError('Please select a PDF file first');
      return;
    }

    console.log('Starting course outline generation...');
    console.log('Selected file:', selectedFile.name, selectedFile.size);
    console.log('Form data:', formData);

    setIsLoading(true);
    setStreamingText('');
    setError(null);

    try {
      console.log('Calling stream-enabled generateCourse...');

      // Use stream-enabled function
      const outline = await generateCourse(
        selectedFile,
        {
          courseName: formData.courseName,
          courseDescription: formData.courseDescription,
          targetAudience: formData.targetAudience,
          difficulty: formData.difficulty
        },
        (chunk) => {
          setStreamingText(chunk);
        }
      );

      console.log('Generated outline:', outline);

      setCourseOutline(outline);
      setStep(2);

      console.log('Successfully generated outline and moved to step 2');
    } catch (error: any) {
      console.error('Error generating outline:', error);

      // Better error handling with fallback
      if (error.message?.includes('API key')) {
        setError('Gemini API key is not configured. Using fallback structure.');
        // Use fallback outline
        const fallbackOutline = createFallbackOutline(formData);
        setCourseOutline(fallbackOutline);
        setStep(2);
      } else if (error.message?.includes('quota')) {
        setError('API quota exceeded. Using fallback structure.');
        const fallbackOutline = createFallbackOutline(formData);
        setCourseOutline(fallbackOutline);
        setStep(2);
      } else {
        setError(
          error.message ||
            'Failed to generate course outline. Please try again.'
        );
      }
    } finally {
      setIsLoading(false);
    }
  };

  const createFallbackOutline = (courseData: any) => {
    return {
      courseName: courseData.courseName || 'Generated Course',
      description:
        courseData.courseDescription ||
        'AI-generated course description (using fallback)',
      modules: [
        {
          name: 'Introduction and Fundamentals',
          description: 'Basic concepts and introduction to the subject',
          chapters: [
            {
              name: 'Chapter 1: Overview',
              description: 'Introduction to the topic',
              hasQuiz: true
            },
            {
              name: 'Chapter 2: Basic Concepts',
              description: 'Fundamental principles',
              hasQuiz: true
            },
            {
              name: 'Chapter 3: Key Terms',
              description: 'Important terminology',
              hasQuiz: true
            }
          ],
          hasModuleQuiz: true
        },
        {
          name: 'Core Concepts',
          description: 'Main topics and detailed explanations',
          chapters: [
            {
              name: 'Chapter 4: Advanced Topics',
              description: 'In-depth coverage',
              hasQuiz: true
            },
            {
              name: 'Chapter 5: Practical Applications',
              description: 'Real-world examples',
              hasQuiz: true
            }
          ],
          hasModuleQuiz: true
        },
        {
          name: 'Advanced Topics',
          description: 'Complex concepts and case studies',
          chapters: [
            {
              name: 'Chapter 6: Case Studies',
              description: 'Practical examples',
              hasQuiz: true
            },
            {
              name: 'Chapter 7: Future Trends',
              description: 'Emerging developments',
              hasQuiz: true
            }
          ],
          hasModuleQuiz: true
        }
      ],
      hasFinalExam: true
    };
  };

  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove data URL prefix to get base64 string
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = (error) => reject(error);
    });
  };

  const handleCreateCourse = async () => {
    setIsLoading(true);
    setError(null);

    try {
      console.log('Creating course with outline:', courseOutline);

      // Prepare PDF data if file is selected
      let pdfData = null;
      if (selectedFile) {
        try {
          const base64Data = await convertFileToBase64(selectedFile);
          pdfData = {
            name: selectedFile.name,
            data: base64Data
          };
          console.log('PDF data prepared for API');
        } catch (error) {
          console.warn('Failed to convert PDF to base64:', error);
          // Continue without PDF data
        }
      }

      // Call API to create course with integrated quizzes and PDF support
      const response = await fetch('/api/courses/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          courseOutline,
          teacherId: 1, // TODO: Get from auth context
          institutionId: 1, // TODO: Get from auth context (optional)
          pdfFile: pdfData // Include PDF for optimized content generation
        })
      });

      const data = await response.json();
      console.log('API response:', data);

      if (!response.ok || !data.success) {
        throw new Error(data.error || 'Failed to create course');
      }

      // Show success message
      alert(
        'Course created successfully with integrated quizzes and AI-generated content!'
      );

      // Redirect to courses list
      router.push('/dashboard/teacher/courses');
    } catch (error: any) {
      console.error('Error creating course:', error);
      setError(error.message || 'Failed to create course. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center space-x-4'>
        <Link href='/dashboard/teacher/courses'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            AI Course Generator
          </h1>
          <p className='text-muted-foreground'>
            Generate a complete course from your PDF materials using optimized
            AI processing
          </p>
        </div>
      </div>

      {/* Progress Indicator */}
      <Card>
        <CardContent className='pt-6'>
          <div className='mb-2 flex items-center justify-between'>
            <span className='text-sm font-medium'>Step {step} of 2</span>
            <span className='text-muted-foreground text-sm'>
              {step === 1 ? 'Upload & Configure' : 'Review & Create'}
            </span>
          </div>
          <Progress value={step === 1 ? 50 : 100} className='h-2' />
        </CardContent>
      </Card>

      {/* Error Display */}
      {error && (
        <Card className='border-red-200 bg-red-50'>
          <CardContent className='pt-6'>
            <div className='flex items-center space-x-2'>
              <div className='h-4 w-4 rounded-full bg-red-500'></div>
              <p className='text-sm font-medium text-red-700'>
                {error.includes('fallback') || error.includes('quota')
                  ? `Notice: ${error}`
                  : `Error: ${error}`}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {step === 1 && (
        <>
          {/* Course Information */}
          <Card>
            <CardHeader>
              <CardTitle>Course Information</CardTitle>
              <CardDescription>
                Provide basic information about your course
              </CardDescription>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='courseName'>Course Name</Label>
                  <Input
                    id='courseName'
                    value={formData.courseName}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        courseName: e.target.value
                      }))
                    }
                    placeholder='e.g., Introduction to Mathematics'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='courseType'>Course Type</Label>
                  <Select
                    value={formData.courseType}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, courseType: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='self_paced'>Self-paced</SelectItem>
                      <SelectItem value='verified'>Verified</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='courseDescription'>Course Description</Label>
                <Textarea
                  id='courseDescription'
                  value={formData.courseDescription}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      courseDescription: e.target.value
                    }))
                  }
                  placeholder='Brief description of what this course covers'
                  rows={3}
                />
              </div>

              <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                <div className='space-y-2'>
                  <Label htmlFor='targetAudience'>Target Audience</Label>
                  <Input
                    id='targetAudience'
                    value={formData.targetAudience}
                    onChange={(e) =>
                      setFormData((prev) => ({
                        ...prev,
                        targetAudience: e.target.value
                      }))
                    }
                    placeholder='e.g., High school students'
                  />
                </div>
                <div className='space-y-2'>
                  <Label htmlFor='difficulty'>Difficulty Level</Label>
                  <Select
                    value={formData.difficulty}
                    onValueChange={(value) =>
                      setFormData((prev) => ({ ...prev, difficulty: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='beginner'>Beginner</SelectItem>
                      <SelectItem value='intermediate'>Intermediate</SelectItem>
                      <SelectItem value='advanced'>Advanced</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* PDF Upload */}
          <Card>
            <CardHeader>
              <CardTitle>Upload Course Material</CardTitle>
              <CardDescription>
                Upload a PDF file that contains your course content. The AI will
                analyze it directly to generate modules, chapters, and quizzes
                with optimized processing.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='space-y-4'>
                <div className='border-muted-foreground/25 rounded-lg border-2 border-dashed p-6'>
                  <div className='flex flex-col items-center text-center'>
                    <FileText className='text-muted-foreground h-12 w-12' />
                    <div className='mt-4'>
                      <Label htmlFor='pdf-upload' className='cursor-pointer'>
                        <span className='text-primary hover:text-primary/80 text-sm font-medium'>
                          Click to upload PDF
                        </span>
                        <Input
                          id='pdf-upload'
                          type='file'
                          accept='.pdf'
                          onChange={handleFileSelect}
                          className='hidden'
                        />
                      </Label>
                      <p className='text-muted-foreground mt-1 text-sm'>
                        PDF files up to 50MB
                      </p>
                    </div>
                  </div>
                </div>

                {selectedFile && (
                  <div className='bg-muted flex items-center space-x-2 rounded-lg p-3'>
                    <FileText className='text-muted-foreground h-5 w-5' />
                    <span className='text-sm font-medium'>
                      {selectedFile.name}
                    </span>
                    <span className='text-muted-foreground text-sm'>
                      ({(selectedFile.size / 1024 / 1024).toFixed(2)} MB)
                    </span>
                    <Badge variant='secondary' className='text-xs'>
                      Ready for AI Processing
                    </Badge>
                  </div>
                )}

                {isLoading && (
                  <div className='space-y-3'>
                    <div className='flex items-center space-x-2'>
                      <Loader2 className='h-4 w-4 animate-spin' />
                      <span className='text-sm font-medium'>
                        Generating course outline...
                      </span>
                    </div>
                    <div className='bg-muted rounded-md border p-3'>
                      <p className='text-muted-foreground font-mono text-xs whitespace-pre-wrap'>
                        {streamingText || 'Waiting for AI response...'}
                      </p>
                    </div>
                  </div>
                )}

                <Button
                  onClick={handleGenerateOutline}
                  disabled={!selectedFile || isLoading}
                  className='w-full'
                >
                  <Bot className='mr-2 h-4 w-4' />
                  {isLoading
                    ? 'Generating Outline...'
                    : 'Generate Course Outline (Optimized)'}
                </Button>

                {selectedFile && (
                  <p className='text-muted-foreground text-center text-xs'>
                    ✨ Using optimized processing: Direct PDF → Course Structure
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {step === 2 && courseOutline && (
        <Card>
          <CardHeader>
            <CardTitle>Generated Course Outline</CardTitle>
            <CardDescription>
              Review the AI-generated course structure. Content and quizzes will
              be generated from your PDF during creation.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className='space-y-6'>
              <div>
                <h3 className='text-lg font-semibold'>
                  {courseOutline.courseName}
                </h3>
                <p className='text-muted-foreground'>
                  {courseOutline.description}
                </p>
                {selectedFile && (
                  <div className='mt-2'>
                    <Badge variant='outline' className='text-xs'>
                      Content will be generated from: {selectedFile.name}
                    </Badge>
                  </div>
                )}
              </div>

              <div className='space-y-4'>
                {courseOutline.modules.map(
                  (module: any, moduleIndex: number) => (
                    <div key={moduleIndex} className='rounded-lg border p-4'>
                      <div className='mb-2 flex items-center justify-between'>
                        <h4 className='font-medium'>
                          Module {moduleIndex + 1}: {module.name}
                        </h4>
                        <div className='flex space-x-2'>
                          {module.hasModuleQuiz && (
                            <Badge variant='secondary' className='text-xs'>
                              Module Quiz
                            </Badge>
                          )}
                          <Badge variant='outline' className='text-xs'>
                            AI Generated
                          </Badge>
                        </div>
                      </div>
                      <p className='text-muted-foreground mb-3 text-sm'>
                        {module.description}
                      </p>
                      <div className='space-y-2'>
                        {module.chapters.map(
                          (chapter: any, chapterIndex: number) => (
                            <div
                              key={chapterIndex}
                              className='bg-muted ml-4 rounded p-2'
                            >
                              <div className='flex items-center justify-between'>
                                <div>
                                  <p className='text-sm font-medium'>
                                    {chapter.name}
                                  </p>
                                  <p className='text-muted-foreground text-xs'>
                                    {chapter.description}
                                  </p>
                                </div>
                                <div className='flex space-x-1'>
                                  {chapter.hasQuiz && (
                                    <Badge
                                      variant='outline'
                                      className='text-xs'
                                    >
                                      Quiz
                                    </Badge>
                                  )}
                                  <Badge
                                    variant='default'
                                    className='bg-green-100 text-xs text-green-800'
                                  >
                                    PDF Content
                                  </Badge>
                                </div>
                              </div>
                            </div>
                          )
                        )}
                      </div>
                    </div>
                  )
                )}

                {courseOutline.hasFinalExam && (
                  <div className='rounded-lg border bg-gradient-to-r from-blue-50 to-indigo-50 p-4'>
                    <div className='flex items-center justify-between'>
                      <div>
                        <h4 className='font-medium'>Final Examination</h4>
                        <p className='text-muted-foreground text-sm'>
                          Comprehensive exam covering all course materials from
                          PDF
                        </p>
                      </div>
                      <div className='flex space-x-2'>
                        <Badge variant='default'>Final Exam</Badge>
                        <Badge variant='outline' className='text-xs'>
                          PDF Based
                        </Badge>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              <div className='rounded-lg border border-blue-200 bg-blue-50 p-4'>
                <div className='flex items-start space-x-3'>
                  <Bot className='mt-0.5 h-5 w-5 text-blue-600' />
                  <div>
                    <h4 className='text-sm font-medium text-blue-900'>
                      Optimized AI Processing
                    </h4>
                    <p className='mt-1 text-sm text-blue-700'>
                      When you create this course, the AI will generate detailed
                      content and quizzes directly from your PDF using optimized
                      batch processing for better efficiency and accuracy.
                    </p>
                  </div>
                </div>
              </div>

              <div className='flex justify-end space-x-4'>
                <Button variant='outline' onClick={() => setStep(1)}>
                  Back to Edit
                </Button>
                <Button onClick={handleCreateCourse} disabled={isLoading}>
                  <Upload className='mr-2 h-4 w-4' />
                  {isLoading
                    ? 'Creating Course...'
                    : 'Create Course with AI Content'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
