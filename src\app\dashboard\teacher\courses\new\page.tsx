'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { ArrowLeft, Save, Calendar } from 'lucide-react';
import Link from 'next/link';
import { authStorage } from '@/lib/auth';
import { toast } from 'sonner';

export default function NewCoursePage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'self_paced',
    startDate: '',
    endDate: '',
    courseCode: ''
  });

  const generateCourseCode = () => {
    const code = Math.random().toString(36).substring(2, 8).toUpperCase();
    setFormData((prev) => ({ ...prev, courseCode: code }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.name.trim()) {
      toast.error('Course name is required');
      return;
    }

    setIsLoading(true);

    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to create courses');
        return;
      }

      const response = await fetch('/api/courses', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          ...formData,
          teacherId: user.id
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Course created successfully!');
        router.push('/dashboard/teacher/courses');
      } else {
        toast.error(data.error || 'Failed to create course');
      }
    } catch (error) {
      console.error('Error creating course:', error);
      toast.error('Failed to create course');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className='space-y-6'>
      <div className='flex items-center space-x-4'>
        <Link href='/dashboard/teacher/courses'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Create New Course
          </h1>
          <p className='text-muted-foreground'>
            Create a new course manually or use our AI generator
          </p>
        </div>
      </div>

      <div className='grid grid-cols-1 gap-6 lg:grid-cols-3'>
        <div className='lg:col-span-2'>
          <Card>
            <CardHeader>
              <CardTitle>Course Details</CardTitle>
              <CardDescription>
                Enter the basic information for your new course
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className='space-y-6'>
                <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
                  <div className='space-y-2'>
                    <Label htmlFor='name'>Course Name</Label>
                    <Input
                      id='name'
                      value={formData.name}
                      onChange={(e) =>
                        handleInputChange('name', e.target.value)
                      }
                      placeholder='e.g., Introduction to Mathematics'
                      required
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='courseCode'>Course Code</Label>
                    <div className='flex space-x-2'>
                      <Input
                        id='courseCode'
                        value={formData.courseCode}
                        onChange={(e) =>
                          handleInputChange('courseCode', e.target.value)
                        }
                        placeholder='e.g., MATH101'
                      />
                      <Button
                        type='button'
                        variant='outline'
                        onClick={generateCourseCode}
                      >
                        Generate
                      </Button>
                    </div>
                  </div>
                </div>

                <div className='space-y-2'>
                  <Label htmlFor='description'>Description</Label>
                  <Textarea
                    id='description'
                    value={formData.description}
                    onChange={(e) =>
                      handleInputChange('description', e.target.value)
                    }
                    placeholder='Brief description of what this course covers'
                    rows={4}
                  />
                </div>

                <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
                  <div className='space-y-2'>
                    <Label htmlFor='type'>Course Type</Label>
                    <Select
                      value={formData.type}
                      onValueChange={(value) =>
                        handleInputChange('type', value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value='self_paced'>Self-paced</SelectItem>
                        <SelectItem value='verified'>Verified</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='startDate'>Start Date</Label>
                    <Input
                      id='startDate'
                      type='date'
                      value={formData.startDate}
                      onChange={(e) =>
                        handleInputChange('startDate', e.target.value)
                      }
                    />
                  </div>

                  <div className='space-y-2'>
                    <Label htmlFor='endDate'>End Date</Label>
                    <Input
                      id='endDate'
                      type='date'
                      value={formData.endDate}
                      onChange={(e) =>
                        handleInputChange('endDate', e.target.value)
                      }
                    />
                  </div>
                </div>

                <div className='flex justify-end space-x-4'>
                  <Link href='/dashboard/teacher/courses'>
                    <Button variant='outline' type='button'>
                      Cancel
                    </Button>
                  </Link>
                  <Button type='submit' disabled={isLoading}>
                    <Save className='mr-2 h-4 w-4' />
                    {isLoading ? 'Creating...' : 'Create Course'}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>

        <div className='space-y-6'>
          {/* Course Type Info */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>Course Types</CardTitle>
            </CardHeader>
            <CardContent className='space-y-4'>
              <div>
                <h4 className='text-sm font-medium'>Self-paced</h4>
                <p className='text-muted-foreground text-xs'>
                  Students can complete at their own pace. Certificates are
                  automatically generated upon completion.
                </p>
              </div>
              <div>
                <h4 className='text-sm font-medium'>Verified</h4>
                <p className='text-muted-foreground text-xs'>
                  Requires manual verification by instructor before certificate
                  generation.
                </p>
              </div>
            </CardContent>
          </Card>

          {/* AI Alternative */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>Need Help?</CardTitle>
              <CardDescription>
                Let AI generate your course structure
              </CardDescription>
            </CardHeader>
            <CardContent>
              <p className='text-muted-foreground mb-4 text-sm'>
                Upload a PDF and let our AI create a complete course outline
                with modules and chapters.
              </p>
              <Link href='/dashboard/teacher/courses/generate'>
                <Button variant='outline' className='w-full'>
                  Try AI Generator
                </Button>
              </Link>
            </CardContent>
          </Card>

          {/* Next Steps */}
          <Card>
            <CardHeader>
              <CardTitle className='text-lg'>Next Steps</CardTitle>
            </CardHeader>
            <CardContent>
              <ul className='space-y-2 text-sm'>
                <li className='flex items-center space-x-2'>
                  <span className='h-2 w-2 rounded-full bg-blue-500'></span>
                  <span>Add modules and chapters</span>
                </li>
                <li className='flex items-center space-x-2'>
                  <span className='h-2 w-2 rounded-full bg-blue-500'></span>
                  <span>Create quizzes and assessments</span>
                </li>
                <li className='flex items-center space-x-2'>
                  <span className='h-2 w-2 rounded-full bg-blue-500'></span>
                  <span>Assign to classes</span>
                </li>
                <li className='flex items-center space-x-2'>
                  <span className='h-2 w-2 rounded-full bg-blue-500'></span>
                  <span>Share course code with students</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
