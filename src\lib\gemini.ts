import { GoogleGenAI } from '@google/genai';
import { CourseOutline, GeneratedContent } from '@/types/database';

// Initialize Gemini AI
const getGeminiAI = () => {
  const apiKey = process.env.NEXT_PUBLIC_GEMINI_API_KEY;
  if (!apiKey) {
    throw new Error('GEMINI_API_KEY is not configured');
  }
  return new GoogleGenAI({ apiKey });
};

// Generate course outline directly from PDF
export const generateCourse = async (
  file: File,
  courseInfo: {
    courseName?: string;
    courseDescription?: string;
    targetAudience?: string;
    difficulty?: string;
  },
  onStreamUpdate: (chunk: string) => void
): Promise<CourseOutline> => {
  try {
    const ai = getGeminiAI();

    // Convert file to base64
    const arrayBuffer = await file.arrayBuffer();
    const base64Content = Buffer.from(arrayBuffer).toString('base64');

    const contents = [
      {
        text: `Analyze this PDF document and create a comprehensive course outline directly from it.

        Course Information:
        - Name: ${courseInfo.courseName || 'Generated Course'}
        - Description: ${courseInfo.courseDescription || 'AI-generated course'}
        - Target Audience: ${courseInfo.targetAudience || 'General learners'}
        - Difficulty Level: ${courseInfo.difficulty || 'Beginner'}

        IMPORTANT: Create a course structure that includes quizzes at different levels:
        1. Chapter Quiz - after each chapter
        2. Module Quiz - after completing all chapters in a module
        3. Final Exam - after completing all modules

        Analyze the PDF content and create a structured course outline with the following JSON format:
        {
          "courseName": "Course Name",
          "description": "Course description",
          "modules": [
            {
              "name": "Module Name",
              "description": "Module description",
              "chapters": [
                {
                  "name": "Chapter Name",
                  "description": "Chapter description",
                  "hasQuiz": true
                }
              ],
              "hasModuleQuiz": true
            }
          ],
          "hasFinalExam": true
        }

        Requirements:
        - 3-6 modules total based on PDF content structure
        - 3-5 chapters per module based on natural content divisions
        - Each chapter has a quiz
        - Each module has a comprehensive quiz
        - Course has a final exam
        - Content follows logical learning progression from the PDF
        - Appropriate for the target audience and difficulty level

        Return ONLY the JSON, no additional text.`
      },
      {
        inlineData: {
          mimeType: 'application/pdf',
          data: base64Content
        }
      }
    ];

    const stream = await ai.models.generateContentStream({
      model: 'gemini-2.5-flash',
      contents: contents
    });

    let fullText = '';
    for await (const chunk of stream) {
      const chunkText = chunk.text;
      if (chunkText) {
        fullText += chunkText;
        onStreamUpdate(fullText);
      }
    }

    // Parse the JSON response
    try {
      const jsonMatch = fullText.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const outline = JSON.parse(jsonMatch[0]);
        return outline as CourseOutline;
      } else {
        throw new Error('No valid JSON found in response');
      }
    } catch (parseError) {
      console.error('Error parsing JSON response:', parseError);
      return createFallbackOutline(courseInfo);
    }
  } catch (error) {
    console.error('Error generating course outline from PDF:', error);
    throw new Error('Failed to generate course outline from PDF');
  }
};

// Generate content for a specific chapter directly from PDF
export const generateChapterContent = async (
  file: File,
  chapterName: string,
  chapterDescription: string,
  moduleContext: string,
  courseContext: string
): Promise<GeneratedContent> => {
  try {
    const ai = getGeminiAI();

    // Convert file to base64
    const arrayBuffer = await file.arrayBuffer();
    const base64Content = Buffer.from(arrayBuffer).toString('base64');

    const contents = [
      {
        text: `Generate comprehensive educational content for a specific chapter by analyzing the PDF document.

        Context:
        - Course: ${courseContext}
        - Module: ${moduleContext}
        - Chapter: ${chapterName}
        - Description: ${chapterDescription}

        Please generate content specifically for "${chapterName}" by:
        1. Finding relevant sections in the PDF that relate to this chapter
        2. Creating detailed chapter content in Markdown format (1000-2000 words)
        3. Creating a chapter quiz with 5-10 questions based on the PDF content

        The content should be:
        - Educational and engaging
        - Well-structured with headings and subheadings
        - Include examples and practical applications from the PDF
        - Appropriate for the learning level
        - Focused specifically on the chapter topic

        Return the response in the following JSON format:
        {
          "content": "# Chapter Title\\n\\nMarkdown content here...",
          "quiz": {
            "name": "Chapter Quiz: ${chapterName}",
            "description": "Quiz for ${chapterName}",
            "timeLimit": 20,
            "minimumScore": 70,
            "questions": [
              {
                "type": "multiple_choice",
                "question": "Question text based on PDF content",
                "options": ["Option A", "Option B", "Option C", "Option D"],
                "correctAnswer": "Option A",
                "points": 1
              },
              {
                "type": "true_false",
                "question": "Question text based on PDF content",
                "correctAnswer": "true",
                "points": 1
              },
              {
                "type": "essay",
                "question": "Question text based on PDF content",
                "correctAnswer": "Sample answer or key points",
                "points": 3
              }
            ]
          }
        }

        Return ONLY the JSON, no additional text.`
      },
      {
        inlineData: {
          mimeType: 'application/pdf',
          data: base64Content
        }
      }
    ];

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: contents,
      config: {
        thinkingConfig: {
          thinkingBudget: 0 // Disables thinking
        }
      }
    });

    const text = response.text || '';

    // Parse the JSON response
    try {
      const jsonMatch = text.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const content = JSON.parse(jsonMatch[0]);
        return content as GeneratedContent;
      } else {
        throw new Error('No valid JSON found in response');
      }
    } catch (parseError) {
      console.error('Error parsing JSON response:', parseError);
      return createFallbackContent(chapterName, chapterDescription);
    }
  } catch (error) {
    console.error('Error generating chapter content from PDF:', error);
    throw new Error('Failed to generate chapter content from PDF');
  }
};

// Generate module quiz directly from PDF
export const generateModuleQuiz = async (
  file: File,
  moduleName: string,
  moduleDescription: string,
  chapters: string[],
  courseContext: string
): Promise<any> => {
  try {
    const ai = getGeminiAI();

    // Convert file to base64
    const arrayBuffer = await file.arrayBuffer();
    const base64Content = Buffer.from(arrayBuffer).toString('base64');

    const contents = [
      {
        text: `Generate a comprehensive module quiz by analyzing the PDF document.

        Context:
        - Course: ${courseContext}
        - Module: ${moduleName}
        - Description: ${moduleDescription}
        - Chapters covered: ${chapters.join(', ')}

        Create a module quiz that:
        1. Tests understanding of content from the PDF related to this module
        2. Has 10-15 questions of mixed types based on PDF content
        3. Includes more challenging questions than chapter quizzes
        4. Covers all chapters in the module comprehensively

        Return the response in the following JSON format:
        {
          "name": "Module Quiz: ${moduleName}",
          "description": "Comprehensive quiz covering all topics in ${moduleName}",
          "timeLimit": 45,
          "minimumScore": 75,
          "questions": [
            {
              "type": "multiple_choice",
              "question": "Question text based on PDF content",
              "options": ["Option A", "Option B", "Option C", "Option D"],
              "correctAnswer": "Option A",
              "points": 2
            },
            {
              "type": "true_false",
              "question": "Question text based on PDF content",
              "correctAnswer": "true",
              "points": 1
            },
            {
              "type": "essay",
              "question": "Question text based on PDF content",
              "correctAnswer": "Sample answer or key points",
              "points": 5
            }
          ]
        }

        Return ONLY the JSON, no additional text.`
      },
      {
        inlineData: {
          mimeType: 'application/pdf',
          data: base64Content
        }
      }
    ];

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: contents
    });

    const text = response.text || '';
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    throw new Error('Failed to generate module quiz from PDF');
  } catch (error) {
    console.error('Error generating module quiz from PDF:', error);
    throw new Error('Failed to generate module quiz from PDF');
  }
};

// Generate final exam directly from PDF
export const generateFinalExam = async (
  file: File,
  courseName: string,
  courseDescription: string,
  modules: string[]
): Promise<any> => {
  try {
    const ai = getGeminiAI();

    // Convert file to base64
    const arrayBuffer = await file.arrayBuffer();
    const base64Content = Buffer.from(arrayBuffer).toString('base64');

    const contents = [
      {
        text: `Generate a comprehensive final exam by analyzing the entire PDF document.

        Context:
        - Course: ${courseName}
        - Description: ${courseDescription}
        - Modules covered: ${modules.join(', ')}

        Create a final exam that:
        1. Tests understanding across the entire PDF content
        2. Has 20-30 questions of mixed types covering all major topics
        3. Includes the most challenging questions from the PDF material
        4. Tests both knowledge and application of concepts
        5. Represents a comprehensive assessment of the entire document

        Return the response in the following JSON format:
        {
          "name": "Final Exam: ${courseName}",
          "description": "Comprehensive final examination covering all course materials",
          "timeLimit": 90,
          "minimumScore": 80,
          "questions": [
            {
              "type": "multiple_choice",
              "question": "Question text based on PDF content",
              "options": ["Option A", "Option B", "Option C", "Option D"],
              "correctAnswer": "Option A",
              "points": 3
            },
            {
              "type": "true_false",
              "question": "Question text based on PDF content",
              "correctAnswer": "true",
              "points": 2
            },
            {
              "type": "essay",
              "question": "Question text based on PDF content",
              "correctAnswer": "Sample answer or key points",
              "points": 10
            }
          ]
        }

        Return ONLY the JSON, no additional text.`
      },
      {
        inlineData: {
          mimeType: 'application/pdf',
          data: base64Content
        }
      }
    ];

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: contents
    });

    const text = response.text || '';
    const jsonMatch = text.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      return JSON.parse(jsonMatch[0]);
    }
    throw new Error('Failed to generate final exam from PDF');
  } catch (error) {
    console.error('Error generating final exam from PDF:', error);
    throw new Error('Failed to generate final exam from PDF');
  }
};

// Optimized batch content generation - generate multiple chapters at once
export const generateBatchChapterContent = async (
  file: File,
  chapters: Array<{
    name: string;
    description: string;
    moduleContext: string;
  }>,
  courseContext: string
): Promise<GeneratedContent[]> => {
  try {
    const ai = getGeminiAI();

    // Convert file to base64 once
    const arrayBuffer = await file.arrayBuffer();
    const base64Content = Buffer.from(arrayBuffer).toString('base64');

    const chapterList = chapters
      .map((ch) => `- ${ch.name}: ${ch.description}`)
      .join('\n');

    const contents = [
      {
        text: `Generate comprehensive educational content for multiple chapters by analyzing the PDF document.

        Course Context: ${courseContext}
        
        Chapters to generate:
        ${chapterList}

        For each chapter, generate:
        1. Detailed content in Markdown format (800-1500 words each)
        2. A chapter quiz with 5-8 questions based on PDF content

        Return the response as a JSON array:
        [
          {
            "chapterName": "Chapter Name",
            "content": "# Chapter Title\\n\\nMarkdown content here...",
            "quiz": {
              "name": "Chapter Quiz: Chapter Name",
              "description": "Quiz for Chapter Name",
              "timeLimit": 20,
              "minimumScore": 70,
              "questions": [...]
            }
          }
        ]

        Return ONLY the JSON array, no additional text.`
      },
      {
        inlineData: {
          mimeType: 'application/pdf',
          data: base64Content
        }
      }
    ];

    const response = await ai.models.generateContent({
      model: 'gemini-2.5-flash',
      contents: contents
    });

    const text = response.text || '';
    const jsonMatch = text.match(/\[[\s\S]*\]/);
    if (jsonMatch) {
      const batchContent = JSON.parse(jsonMatch[0]);
      return batchContent.map((item: any) => ({
        content: item.content,
        quiz: item.quiz
      }));
    }
    throw new Error('Failed to generate batch content');
  } catch (error) {
    console.error('Error generating batch chapter content:', error);
    // Return fallback content for each chapter
    return chapters.map((ch) => createFallbackContent(ch.name, ch.description));
  }
};

// Helper functions remain the same
const createFallbackOutline = (courseInfo: any): CourseOutline => {
  return {
    courseName: courseInfo.courseName || 'Generated Course',
    description:
      courseInfo.courseDescription || 'AI-generated course description',
    modules: [
      {
        name: 'Introduction and Fundamentals',
        description: 'Basic concepts and introduction to the subject',
        chapters: [
          {
            name: 'Chapter 1: Overview',
            description: 'Introduction to the topic',
            hasQuiz: true
          },
          {
            name: 'Chapter 2: Basic Concepts',
            description: 'Fundamental principles',
            hasQuiz: true
          },
          {
            name: 'Chapter 3: Key Terms',
            description: 'Important terminology',
            hasQuiz: true
          }
        ],
        hasModuleQuiz: true
      },
      {
        name: 'Core Concepts',
        description: 'Main topics and detailed explanations',
        chapters: [
          {
            name: 'Chapter 4: Advanced Topics',
            description: 'In-depth coverage',
            hasQuiz: true
          },
          {
            name: 'Chapter 5: Practical Applications',
            description: 'Real-world examples',
            hasQuiz: true
          }
        ],
        hasModuleQuiz: true
      },
      {
        name: 'Advanced Topics',
        description: 'Complex concepts and case studies',
        chapters: [
          {
            name: 'Chapter 6: Case Studies',
            description: 'Practical examples',
            hasQuiz: true
          },
          {
            name: 'Chapter 7: Future Trends',
            description: 'Emerging developments',
            hasQuiz: true
          }
        ],
        hasModuleQuiz: true
      }
    ],
    hasFinalExam: true
  };
};

const createFallbackContent = (
  chapterName: string,
  chapterDescription: string
): GeneratedContent => {
  return {
    content: `# ${chapterName}\n\n${chapterDescription}\n\nThis content will be generated based on your course materials.`,
    quiz: {
      name: `Chapter Quiz: ${chapterName}`,
      description: `Quiz for ${chapterName}`,
      timeLimit: 20,
      minimumScore: 70,
      questions: [
        {
          type: 'multiple_choice',
          question: 'Sample multiple choice question',
          options: ['Option A', 'Option B', 'Option C', 'Option D'],
          correctAnswer: 'Option A',
          points: 1
        },
        {
          type: 'true_false',
          question: 'Sample true/false question',
          correctAnswer: 'true',
          points: 1
        }
      ]
    }
  };
};
