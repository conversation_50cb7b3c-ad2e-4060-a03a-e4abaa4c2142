import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { 
  courses, 
  classes, 
  users, 
  studentEnrollments, 
  courseEnrollments,
  studentProgress,
  quizAttempts,
  modules,
  chapters,
  quizzes
} from '@/lib/db/schema';
import { eq, and, avg, count, sum, desc } from 'drizzle-orm';

// GET /api/reports - Get various analytics and reports
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type'); // 'overview', 'course', 'class', 'student'
    const teacherId = searchParams.get('teacherId');
    const courseId = searchParams.get('courseId');
    const classId = searchParams.get('classId');
    const studentId = searchParams.get('studentId');
    
    if (!teacherId) {
      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });
    }

    if (type === 'overview') {
      // Get teacher overview analytics
      const teacherCourses = await db
        .select({ id: courses.id })
        .from(courses)
        .where(eq(courses.teacherId, parseInt(teacherId)));
      
      const courseIds = teacherCourses.map(c => c.id);
      
      if (courseIds.length === 0) {
        return NextResponse.json({
          totalCourses: 0,
          totalStudents: 0,
          totalClasses: 0,
          averageProgress: 0,
          recentActivity: []
        });
      }

      // Total courses
      const totalCourses = courseIds.length;

      // Total students enrolled
      const studentEnrollmentCount = await db
        .select({ count: count() })
        .from(studentEnrollments)
        .where(eq(studentEnrollments.courseId, courseIds[0])); // This needs to be fixed for multiple courses
      
      // Get unique students across all courses
      const uniqueStudents = await db
        .selectDistinct({ studentId: studentEnrollments.studentId })
        .from(studentEnrollments)
        .where(eq(studentEnrollments.courseId, courseIds[0])); // This needs to be fixed

      // Total classes with enrolled courses
      const enrolledClasses = await db
        .selectDistinct({ classId: courseEnrollments.classId })
        .from(courseEnrollments)
        .where(eq(courseEnrollments.courseId, courseIds[0])); // This needs to be fixed

      // Average progress across all students
      const avgProgress = await db
        .select({ average: avg(studentEnrollments.progress) })
        .from(studentEnrollments)
        .where(eq(studentEnrollments.courseId, courseIds[0])); // This needs to be fixed

      // Recent quiz attempts
      const recentActivity = await db
        .select({
          id: quizAttempts.id,
          studentName: users.name,
          courseName: courses.name,
          score: quizAttempts.score,
          maxScore: quizAttempts.maxScore,
          completedAt: quizAttempts.completedAt
        })
        .from(quizAttempts)
        .leftJoin(users, eq(quizAttempts.studentId, users.id))
        .leftJoin(quizzes, eq(quizAttempts.quizId, quizzes.id))
        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
        .leftJoin(modules, eq(chapters.moduleId, modules.id))
        .leftJoin(courses, eq(modules.courseId, courses.id))
        .where(eq(courses.teacherId, parseInt(teacherId)))
        .orderBy(desc(quizAttempts.completedAt))
        .limit(10);

      return NextResponse.json({
        totalCourses,
        totalStudents: uniqueStudents.length,
        totalClasses: enrolledClasses.length,
        averageProgress: avgProgress[0]?.average || 0,
        recentActivity
      });
    } else if (type === 'course' && courseId) {
      // Get course-specific analytics
      const course = await db
        .select()
        .from(courses)
        .where(
          and(
            eq(courses.id, parseInt(courseId)),
            eq(courses.teacherId, parseInt(teacherId))
          )
        )
        .limit(1);

      if (course.length === 0) {
        return NextResponse.json({ error: 'Course not found or access denied' }, { status: 403 });
      }

      // Get enrolled students
      const enrolledStudents = await db
        .select({
          id: studentEnrollments.id,
          studentId: studentEnrollments.studentId,
          studentName: users.name,
          studentEmail: users.email,
          progress: studentEnrollments.progress,
          enrolledAt: studentEnrollments.enrolledAt,
          className: classes.name
        })
        .from(studentEnrollments)
        .leftJoin(users, eq(studentEnrollments.studentId, users.id))
        .leftJoin(classes, eq(studentEnrollments.classId, classes.id))
        .where(eq(studentEnrollments.courseId, parseInt(courseId)));

      // Get quiz performance for this course
      const quizPerformance = await db
        .select({
          studentId: quizAttempts.studentId,
          studentName: users.name,
          quizId: quizAttempts.quizId,
          score: quizAttempts.score,
          maxScore: quizAttempts.maxScore,
          completedAt: quizAttempts.completedAt,
          chapterTitle: chapters.title
        })
        .from(quizAttempts)
        .leftJoin(users, eq(quizAttempts.studentId, users.id))
        .leftJoin(quizzes, eq(quizAttempts.quizId, quizzes.id))
        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
        .leftJoin(modules, eq(chapters.moduleId, modules.id))
        .where(eq(modules.courseId, parseInt(courseId)))
        .orderBy(desc(quizAttempts.completedAt));

      // Calculate course statistics
      const totalStudents = enrolledStudents.length;
      const averageProgress = totalStudents > 0 
        ? enrolledStudents.reduce((sum, student) => sum + (student.progress || 0), 0) / totalStudents 
        : 0;
      
      const completedStudents = enrolledStudents.filter(student => (student.progress || 0) >= 100).length;
      const completionRate = totalStudents > 0 ? (completedStudents / totalStudents) * 100 : 0;

      return NextResponse.json({
        course: course[0],
        totalStudents,
        averageProgress,
        completionRate,
        enrolledStudents,
        quizPerformance
      });
    } else if (type === 'class' && classId) {
      // Get class-specific analytics
      const classData = await db
        .select()
        .from(classes)
        .where(eq(classes.id, parseInt(classId)))
        .limit(1);

      if (classData.length === 0) {
        return NextResponse.json({ error: 'Class not found' }, { status: 404 });
      }

      // Get students in this class
      const classStudents = await db
        .select({
          id: users.id,
          name: users.name,
          email: users.email
        })
        .from(users)
        .where(
          and(
            eq(users.classId, parseInt(classId)),
            eq(users.role, 'student')
          )
        );

      // Get courses enrolled by this class
      const enrolledCourses = await db
        .select({
          courseId: courseEnrollments.courseId,
          courseName: courses.name,
          courseCode: courses.courseCode,
          enrolledAt: courseEnrollments.enrolledAt
        })
        .from(courseEnrollments)
        .leftJoin(courses, eq(courseEnrollments.courseId, courses.id))
        .where(
          and(
            eq(courseEnrollments.classId, parseInt(classId)),
            eq(courses.teacherId, parseInt(teacherId))
          )
        );

      // Get student progress in teacher's courses
      const studentProgress = await db
        .select({
          studentId: studentEnrollments.studentId,
          studentName: users.name,
          courseId: studentEnrollments.courseId,
          courseName: courses.name,
          progress: studentEnrollments.progress
        })
        .from(studentEnrollments)
        .leftJoin(users, eq(studentEnrollments.studentId, users.id))
        .leftJoin(courses, eq(studentEnrollments.courseId, courses.id))
        .where(
          and(
            eq(studentEnrollments.classId, parseInt(classId)),
            eq(courses.teacherId, parseInt(teacherId))
          )
        );

      return NextResponse.json({
        class: classData[0],
        totalStudents: classStudents.length,
        students: classStudents,
        enrolledCourses,
        studentProgress
      });
    } else if (type === 'student' && studentId) {
      // Get student-specific analytics
      const student = await db
        .select()
        .from(users)
        .where(
          and(
            eq(users.id, parseInt(studentId)),
            eq(users.role, 'student')
          )
        )
        .limit(1);

      if (student.length === 0) {
        return NextResponse.json({ error: 'Student not found' }, { status: 404 });
      }

      // Get student's course enrollments in teacher's courses
      const studentCourses = await db
        .select({
          enrollmentId: studentEnrollments.id,
          courseId: studentEnrollments.courseId,
          courseName: courses.name,
          courseCode: courses.courseCode,
          progress: studentEnrollments.progress,
          enrolledAt: studentEnrollments.enrolledAt
        })
        .from(studentEnrollments)
        .leftJoin(courses, eq(studentEnrollments.courseId, courses.id))
        .where(
          and(
            eq(studentEnrollments.studentId, parseInt(studentId)),
            eq(courses.teacherId, parseInt(teacherId))
          )
        );

      // Get student's quiz attempts in teacher's courses
      const quizAttemptHistory = await db
        .select({
          id: quizAttempts.id,
          quizId: quizAttempts.quizId,
          score: quizAttempts.score,
          maxScore: quizAttempts.maxScore,
          completedAt: quizAttempts.completedAt,
          chapterTitle: chapters.title,
          courseName: courses.name
        })
        .from(quizAttempts)
        .leftJoin(quizzes, eq(quizAttempts.quizId, quizzes.id))
        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
        .leftJoin(modules, eq(chapters.moduleId, modules.id))
        .leftJoin(courses, eq(modules.courseId, courses.id))
        .where(
          and(
            eq(quizAttempts.studentId, parseInt(studentId)),
            eq(courses.teacherId, parseInt(teacherId))
          )
        )
        .orderBy(desc(quizAttempts.completedAt));

      // Calculate student statistics
      const totalCourses = studentCourses.length;
      const averageProgress = totalCourses > 0 
        ? studentCourses.reduce((sum, course) => sum + (course.progress || 0), 0) / totalCourses 
        : 0;
      
      const completedCourses = studentCourses.filter(course => (course.progress || 0) >= 100).length;
      const totalQuizzes = quizAttemptHistory.length;
      const averageQuizScore = totalQuizzes > 0 
        ? quizAttemptHistory.reduce((sum, quiz) => sum + ((quiz.score / quiz.maxScore) * 100), 0) / totalQuizzes 
        : 0;

      return NextResponse.json({
        student: student[0],
        totalCourses,
        completedCourses,
        averageProgress,
        totalQuizzes,
        averageQuizScore,
        courseEnrollments: studentCourses,
        quizHistory: quizAttemptHistory
      });
    } else {
      return NextResponse.json({ error: 'Invalid report type or missing parameters' }, { status: 400 });
    }
  } catch (error) {
    console.error('Error fetching reports:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}