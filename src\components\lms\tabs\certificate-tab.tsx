import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Award, CheckCircle, XCircle, Lock, Eye, Download } from 'lucide-react';
import { Course, Institution } from '@/types/lms';

interface CertificateTabProps {
  courseData: Course;
  institution: Institution;
  overallProgress: number;
  onGenerateCertificate: () => void;
  onShowCertificate: () => void;
}

export const CertificateTab: React.FC<CertificateTabProps> = ({
  courseData,
  institution,
  overallProgress,
  onGenerateCertificate,
  onShowCertificate
}) => {
  return (
    <Card className='border-2 border-yellow-300'>
      <CardHeader
        className='bg-gradient-to-r from-yellow-50 to-yellow-100'
        style={{
          background: `linear-gradient(to right, ${institution.certificateTemplate?.primaryColor}10, ${institution.certificateTemplate?.secondaryColor}10)`
        }}
      >
        <CardTitle
          className='flex items-center space-x-2'
          style={{ color: institution.certificateTemplate?.primaryColor }}
        >
          <Award className='h-6 w-6' />
          <span>Professional Certification</span>
        </CardTitle>
      </CardHeader>
      <CardContent className='p-6'>
        {courseData.certificate.isEligible &&
        courseData.certificate.isGenerated ? (
          /* Certificate Generated */
          <div className='space-y-6 text-center'>
            <div className='rounded-lg border-2 border-green-200 bg-gradient-to-r from-green-50 to-blue-50 p-8'>
              <Award className='mx-auto mb-4 h-16 w-16 text-green-600' />
              <h3 className='mb-2 text-2xl font-bold text-green-800'>
                Congratulations!
              </h3>
              <p className='text-green-700'>
                You have successfully completed the {courseData.name} course and
                earned your certification.
              </p>
              {courseData.certificate.completionDate && (
                <p className='mt-2 text-sm text-green-600'>
                  Completed on:{' '}
                  {new Date(
                    courseData.certificate.completionDate
                  ).toLocaleDateString()}
                </p>
              )}
            </div>
            <div className='flex justify-center space-x-4'>
              <Button
                onClick={onShowCertificate}
                className='bg-green-600 hover:bg-green-700'
              >
                <Eye className='mr-2 h-4 w-4' />
                View Certificate
              </Button>
              <Button
                variant='outline'
                className='border-green-600 text-green-600 hover:bg-green-50'
              >
                <Download className='mr-2 h-4 w-4' />
                Download PDF
              </Button>
            </div>
          </div>
        ) : courseData.certificate.isEligible ? (
          /* Eligible for Certificate */
          <div className='space-y-6 text-center'>
            <div className='rounded-lg border-2 border-green-200 bg-green-50 p-8'>
              <CheckCircle className='mx-auto mb-4 h-16 w-16 text-green-600' />
              <h3 className='mb-2 text-2xl font-bold text-green-800'>
                Certificate Ready!
              </h3>
              <p className='text-green-700'>
                You have met all requirements for certification. Generate your
                official certificate now.
              </p>
            </div>
            <Button
              onClick={onGenerateCertificate}
              size='lg'
              className='bg-green-600 hover:bg-green-700'
            >
              <Award className='mr-2 h-5 w-5' />
              Generate Certificate
            </Button>
          </div>
        ) : (
          /* Not Eligible Yet */
          <div className='space-y-6'>
            <div className='rounded-lg border-2 border-gray-200 bg-gray-50 p-8 text-center'>
              <Lock className='mx-auto mb-4 h-16 w-16 text-gray-400' />
              <h3 className='mb-2 text-2xl font-bold text-gray-700'>
                Certificate Requirements
              </h3>
              <p className='mb-4 text-gray-600'>
                Complete all course requirements to earn your professional
                certification.
              </p>
            </div>

            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div className='space-y-3'>
                <h4 className='font-medium text-gray-900'>Module Completion</h4>
                {courseData.modules.map((module) => (
                  <div
                    key={module.id}
                    className='flex items-center justify-between rounded-lg bg-gray-50 p-3'
                  >
                    <span className='text-sm'>{module.title}</span>
                    {module.moduleQuiz.isPassed ? (
                      <CheckCircle className='h-5 w-5 text-green-600' />
                    ) : (
                      <XCircle className='h-5 w-5 text-gray-400' />
                    )}
                  </div>
                ))}
              </div>
              <div className='space-y-3'>
                <h4 className='font-medium text-gray-900'>
                  Final Requirements
                </h4>
                <div className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>
                  <span className='text-sm'>
                    Final Exam (Min. {courseData.finalExam.minimumScore}%)
                  </span>
                  {courseData.finalExam.isPassed ? (
                    <CheckCircle className='h-5 w-5 text-green-600' />
                  ) : (
                    <XCircle className='h-5 w-5 text-gray-400' />
                  )}
                </div>
                <div className='flex items-center justify-between rounded-lg bg-gray-50 p-3'>
                  <span className='text-sm'>Overall Score (Min. 70%)</span>
                  {overallProgress >= 70 ? (
                    <CheckCircle className='h-5 w-5 text-green-600' />
                  ) : (
                    <XCircle className='h-5 w-5 text-gray-400' />
                  )}
                </div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
