'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  BookOpen,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Users,
  Bot,
  Copy
} from 'lucide-react';
import Link from 'next/link';
import { authStorage } from '@/lib/auth';
import { toast } from 'sonner';

interface Course {
  id: number;
  name: string;
  description: string;
  type: string;
  courseCode: string;
  moduleCount: number;
  studentCount: number;
  status: string;
  createdAt: string;
  startDate: string;
  endDate: string;
}

export default function CoursesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [courses, setCourses] = useState<Course[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeletingCourse, setIsDeletingCourse] = useState<number | null>(null);

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to view courses');
        return;
      }

      const response = await fetch(`/api/courses?teacherId=${user.id}`);
      const data = await response.json();

      if (data.success) {
        setCourses(data.courses || []);
      } else {
        toast.error(data.error || 'Failed to fetch courses');
      }
    } catch (error) {
      console.error('Error fetching courses:', error);
      toast.error('Failed to fetch courses');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteCourse = async (courseId: number) => {
    if (!confirm('Are you sure you want to delete this course? This action cannot be undone.')) {
      return;
    }

    setIsDeletingCourse(courseId);
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to delete courses');
        return;
      }

      const response = await fetch(`/api/courses/${courseId}?teacherId=${user.id}`, {
        method: 'DELETE'
      });
      const data = await response.json();

      if (data.success) {
        toast.success('Course deleted successfully!');
        fetchCourses();
      } else {
        toast.error(data.error || 'Failed to delete course');
      }
    } catch (error) {
      console.error('Error deleting course:', error);
      toast.error('Failed to delete course');
    } finally {
      setIsDeletingCourse(null);
    }
  };

  const filteredCourses = courses.filter(
    (course) =>
      course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.courseCode.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Course code copied to clipboard!');
  };

  if (isLoading) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto'></div>
          <p className='mt-2'>Loading courses...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>My Courses</h1>
          <p className='text-muted-foreground'>
            Create and manage your educational courses
          </p>
        </div>
        <div className='flex space-x-2'>
          <Link href='/dashboard/teacher/courses/generate'>
            <Button variant='outline'>
              <Bot className='mr-2 h-4 w-4' />
              AI Generator
            </Button>
          </Link>
          <Link href='/dashboard/teacher/courses/new'>
            <Button>
              <Plus className='mr-2 h-4 w-4' />
              Create Course
            </Button>
          </Link>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Courses</CardTitle>
          <CardDescription>View and manage all your courses</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='mb-4 flex items-center space-x-2'>
            <div className='relative flex-1'>
              <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
              <Input
                placeholder='Search courses...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-8'
              />
            </div>
          </div>

          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Course</TableHead>
                  <TableHead>Code</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Modules</TableHead>
                  <TableHead>Students</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className='w-[70px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredCourses.map((course) => (
                  <TableRow key={course.id}>
                    <TableCell>
                      <div className='space-y-1'>
                        <p className='font-medium'>{course.name}</p>
                        <p className='text-muted-foreground text-sm'>
                          {course.description}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-2'>
                        <code className='bg-muted rounded px-2 py-1 text-sm'>
                          {course.courseCode}
                        </code>
                        <Button
                          variant='ghost'
                          size='sm'
                          onClick={() => copyToClipboard(course.courseCode)}
                        >
                          <Copy className='h-3 w-3' />
                        </Button>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          course.type === 'verified' ? 'default' : 'secondary'
                        }
                      >
                        {course.type}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-1'>
                        <BookOpen className='text-muted-foreground h-4 w-4' />
                        <span>{course.moduleCount}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className='flex items-center space-x-1'>
                        <Users className='text-muted-foreground h-4 w-4' />
                        <span>{course.studentCount}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          course.status === 'published' ? 'default' : 'outline'
                        }
                      >
                        {course.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant='ghost' className='h-8 w-8 p-0'>
                            <MoreHorizontal className='h-4 w-4' />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align='end'>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/teacher/courses/${course.id}`}
                            >
                              <Edit className='mr-2 h-4 w-4' />
                              Edit
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/teacher/courses/${course.id}/modules`}
                            >
                              <BookOpen className='mr-2 h-4 w-4' />
                              Manage Modules
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem asChild>
                            <Link
                              href={`/dashboard/teacher/courses/${course.id}/students`}
                            >
                              <Users className='mr-2 h-4 w-4' />
                              View Students
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            className='text-red-600'
                            onClick={() => handleDeleteCourse(course.id)}
                            disabled={isDeletingCourse === course.id}
                          >
                            {isDeletingCourse === course.id ? (
                              <div className='mr-2 h-4 w-4 animate-spin rounded-full border-2 border-red-600 border-t-transparent' />
                            ) : (
                              <Trash2 className='mr-2 h-4 w-4' />
                            )}
                            {isDeletingCourse === course.id ? 'Deleting...' : 'Delete'}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredCourses.length === 0 && (
            <div className='py-8 text-center'>
              <BookOpen className='text-muted-foreground mx-auto h-12 w-12' />
              <h3 className='mt-2 text-sm font-semibold'>No courses found</h3>
              <p className='text-muted-foreground mt-1 text-sm'>
                {searchTerm
                  ? 'Try adjusting your search terms.'
                  : 'Get started by creating a new course.'}
              </p>
              {!searchTerm && (
                <div className='mt-6 flex justify-center space-x-2'>
                  <Link href='/dashboard/teacher/courses/generate'>
                    <Button variant='outline'>
                      <Bot className='mr-2 h-4 w-4' />
                      AI Generator
                    </Button>
                  </Link>
                  <Link href='/dashboard/teacher/courses/new'>
                    <Button>
                      <Plus className='mr-2 h-4 w-4' />
                      Create Course
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
