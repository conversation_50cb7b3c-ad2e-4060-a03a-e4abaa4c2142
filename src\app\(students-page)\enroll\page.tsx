'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  BookOpen,
  Key,
  Lock,
  Users,
  Search,
  CheckCircle2,
  Clock,
  Calendar,
  Mail,
  CheckCircle,
  XCircle,
  ArrowRight,
  Play
} from 'lucide-react';
import {
  architectureClass,
  ClassData as SharedClassData
} from '@/constants/shared-course-data';
import { useEnrollment } from '@/contexts/enrollment-context';
import Link from 'next/link';
import { authStorage } from '@/lib/auth';
import { toast } from 'sonner';

// Types and Interfaces
interface ClassInvitation {
  id: string;
  classId: number;
  status: 'pending' | 'accepted' | 'declined';
  invitedBy: string;
  invitedAt: string;
}

interface ClassData {
  id: number;
  name: string;
  teacher: string;
  schedule: string;
  enrollmentCode: string;
  materials: any[];
  thumbnail?: string;
  description?: string;
  startDate?: string;
  endDate?: string;
}

// Use the shared Architecture course
const mockClasses: ClassData[] = [architectureClass];

const ClassEnrollmentPage: React.FC = () => {
  // Context
  const { isEnrolled, enrollInCourse } = useEnrollment();

  // State management
  const [selectedClass, setSelectedClass] = useState<ClassData | null>(null);
  const [enrollmentCode, setEnrollmentCode] = useState<string>('');
  const [showEnrollModal, setShowEnrollModal] = useState<boolean>(false);
  const [showInvitationModal, setShowInvitationModal] =
    useState<boolean>(false);
  const [selectedInvitation, setSelectedInvitation] =
    useState<ClassInvitation | null>(null);
  const [activeTab, setActiveTab] = useState<string>('available');
  const [error, setError] = useState<string>('');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [invitations, setInvitations] =
    useState<ClassInvitation[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [enrolling, setEnrolling] = useState<boolean>(false);
  const [processingInvitation, setProcessingInvitation] = useState<string | null>(null);

  // Effects
  useEffect(() => {
    fetchData();
  }, []);

  // Handlers
  const fetchData = async () => {
    try {
      setLoading(true);
      const student = authStorage.getUser();
      if (!student || student.role !== 'student') {
        toast.error('Please log in as a student to view enrollments');
        return;
      }

      // Fetch invitations
      const invitationsRes = await fetch(`/api/invitations?studentId=${student.id}`);
      if (invitationsRes.ok) {
        const invitationsData = await invitationsRes.json();
        setInvitations(invitationsData);
      }
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Failed to load enrollment data'); // Use sonner toast directly
    } finally {
      setLoading(false);
    }
  };

  const handleEnrollment = async () => {
    if (!enrollmentCode.trim()) {
      setError('Please enter an enrollment code');
      return;
    }

    try {
      setEnrolling(true);
      const student = authStorage.getUser();
      if (!student || student.role !== 'student') {
        setError('Please log in as a student to enroll');
        return;
      }

      const response = await fetch('/api/enrollments/code', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          code: enrollmentCode,
          studentId: student.id
        })
      });

      if (response.ok) {
        // Enroll in course using context
        enrollInCourse();
        setShowEnrollModal(false);
        setError('');
        setEnrollmentCode('');
        toast.success('Successfully enrolled in class!');
        fetchData(); // Refresh data
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Invalid enrollment code. Please try again.');
      }
    } catch (error) {
      console.error('Error enrolling with code:', error);
      setError('Failed to enroll with code');
    } finally {
      setEnrolling(false);
    }
  };

  const handleInvitationClick = (invitationId: string) => {
    const invitation = invitations.find((inv) => inv.id === invitationId);
    if (invitation) {
      setSelectedInvitation(invitation);
      setShowInvitationModal(true);
    }
  };

  const handleInvitationResponse = async (accept: boolean) => {
    if (!selectedInvitation) return;

    try {
      setProcessingInvitation(selectedInvitation.id);
      const student = authStorage.getUser();
      if (!student || student.role !== 'student') {
        toast.error('Please log in as a student');
        return;
      }

      const response = await fetch(`/api/invitations/${selectedInvitation.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: accept ? 'accept' : 'decline',
          studentId: student.id
        })
      });

      if (response.ok) {
        if (accept && !isEnrolled) {
          enrollInCourse();
        }
        toast.success(`Invitation ${accept ? 'accepted' : 'declined'} successfully!`);
        fetchData(); // Refresh data
      } else {
        const errorData = await response.json();
        toast.error(errorData.message || `Failed to ${accept ? 'accept' : 'decline'} invitation`);
      }
    } catch (error) {
      console.error(`Error ${accept ? 'accepting' : 'declining'} invitation:`, error);
      toast.error(`Failed to ${accept ? 'accept' : 'decline'} invitation`);
    } finally {
      setProcessingInvitation(null);
    }

    setShowInvitationModal(false);
    setSelectedInvitation(null);
  };

  const handleInvitationDecline = (invitationId: string) => {
    setInvitations((prev) =>
      prev.map((inv) =>
        inv.id === invitationId ? { ...inv, status: 'declined' } : inv
      )
    );
    toast.success('Invitation declined.');
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  // Filtered classes based on search
  const filteredClasses = mockClasses.filter(
    (c) =>
      c.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      c.teacher.toLowerCase().includes(searchTerm.toLowerCase()) ||
      c.description?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Pending invitations
  const pendingInvitations = invitations.filter(
    (inv) => inv.status === 'pending'
  );

  return (
    <div className='min-h-screen bg-gray-50 p-8'>
      <div className='mx-auto max-w-4xl space-y-6 pb-8'>
        {/* Header */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-3'>
            <BookOpen className='h-8 w-8 text-blue-600' />
            <div>
              <h1 className='text-2xl font-bold'>
                Class Enrollment & Materials
              </h1>
              <p className='text-sm text-gray-600'>
                Browse available classes or access your enrolled courses
              </p>
            </div>
          </div>
        </div>

        {/* Search Bar - Only show in available classes tab */}
        {activeTab === 'available' && (
          <div className='relative max-w-md'>
            <Search className='absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400' />
            <Input
              placeholder='Search classes...'
              value={searchTerm}
              onChange={handleSearch}
              className='pl-10'
            />
          </div>
        )}

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className='mb-6'>
          <TabsList className='grid w-full grid-cols-2'>
            <TabsTrigger
              value='invitations'
              className='flex items-center space-x-2'
            >
              <Mail className='h-4 w-4' />
              <span>Invitations</span>
              {pendingInvitations.length > 0 && (
                <Badge variant='secondary' className='ml-2'>
                  {pendingInvitations.length}
                </Badge>
              )}
            </TabsTrigger>
            <TabsTrigger
              value='available'
              className='flex items-center space-x-2'
            >
              <BookOpen className='h-4 w-4' />
              <span>Available Classes</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value='invitations' className='mt-4'>
            <div className='grid gap-4'>
              {loading ? (
                // Loading skeleton
                [1, 2, 3].map((i) => (
                  <Card key={i}>
                    <CardContent className='p-6'>
                      <div className='space-y-4'>
                        <div className='flex justify-between items-start'>
                          <div className='space-y-2'>
                            <div className='bg-gray-200 h-6 w-48 rounded animate-pulse'></div>
                            <div className='bg-gray-200 h-4 w-32 rounded animate-pulse'></div>
                          </div>
                          <div className='bg-gray-200 h-6 w-16 rounded animate-pulse'></div>
                        </div>
                        <div className='space-y-2'>
                          <div className='bg-gray-200 h-4 w-40 rounded animate-pulse'></div>
                          <div className='bg-gray-200 h-4 w-36 rounded animate-pulse'></div>
                        </div>
                        <div className='flex space-x-2'>
                          <div className='bg-gray-200 h-9 w-20 rounded animate-pulse'></div>
                          <div className='bg-gray-200 h-9 w-20 rounded animate-pulse'></div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                invitations.map((invitation) => {
                const classData = mockClasses.find(
                  (c) => c.id === invitation.classId
                );
                if (!classData) return null;

                return (
                  <Card
                    key={invitation.id}
                    className='transition-colors hover:bg-gray-50/50'
                  >
                    <CardContent className='p-6'>
                      <div className='flex items-start justify-between'>
                        <div className='space-y-3'>
                          <div>
                            <h3 className='text-lg font-semibold'>
                              {classData.name}
                            </h3>
                            <p className='mt-1 text-gray-600'>
                              Invited by {invitation.invitedBy} on{' '}
                              {new Date(
                                invitation.invitedAt
                              ).toLocaleDateString()}
                            </p>
                          </div>
                          <div className='space-y-1'>
                            <p className='flex items-center text-gray-600'>
                              <Users className='mr-2 h-4 w-4' />
                              Instructor: {classData.teacher}
                            </p>
                            <p className='flex items-center text-gray-600'>
                              <Clock className='mr-2 h-4 w-4' />
                              Schedule: {classData.schedule}
                            </p>
                          </div>
                        </div>
                        {invitation.status === 'pending' ? (
                          <div className='flex space-x-2'>
                            <Button
                              onClick={() =>
                                handleInvitationClick(invitation.id)
                              }
                              className='bg-green-600 hover:bg-green-700'
                            >
                              <CheckCircle className='mr-2 h-4 w-4' />
                              Accept
                            </Button>
                            <Button
                              variant='outline'
                              onClick={() =>
                                handleInvitationDecline(invitation.id)
                              }
                              className='border-red-600 text-red-600 hover:bg-red-50'
                            >
                              <XCircle className='mr-2 h-4 w-4' />
                              Decline
                            </Button>
                          </div>
                        ) : invitation.status === 'accepted' ? (
                          <div className='flex gap-2'>
                            <Badge
                              variant='default'
                              className='bg-green-100 text-green-700'
                            >
                              {invitation.status.charAt(0).toUpperCase() +
                                invitation.status.slice(1)}
                            </Badge>
                            {isEnrolled && (
                              <Link href={`/modules/${invitation.classId}`}>
                                <Button
                                  size='sm'
                                  className='bg-blue-600 hover:bg-blue-700'
                                  disabled={processingInvitation === invitation.id}
                                >
                                  <Play className='mr-2 h-4 w-4' />
                                  Go to Course
                                  <ArrowRight className='ml-2 h-4 w-4' />
                                </Button>
                              </Link>
                            )}
                          </div>
                        ) : (
                          <Badge variant='destructive'>
                            {invitation.status.charAt(0).toUpperCase() +
                              invitation.status.slice(1)}
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                );
                })
              )}
              {!loading && invitations.length === 0 && (
                <div className='rounded-lg bg-gray-50/50 py-12 text-center'>
                  <Mail className='mx-auto mb-4 h-12 w-12 text-gray-400' />
                  <p className='text-lg text-gray-600'>No invitations</p>
                  <p className='text-gray-500'>
                    You will see class invitations from teachers here
                  </p>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value='available' className='mt-4'>
            <div className='grid gap-4'>
              {filteredClasses.map((classItem) => (
                <Card
                  key={classItem.id}
                  className='transition-colors hover:bg-gray-50/50'
                >
                  <CardContent className='p-6'>
                    <div className='flex items-start justify-between'>
                      <div className='space-y-3'>
                        <div className='flex items-center gap-3'>
                          <h3 className='text-lg font-semibold'>
                            {classItem.name}
                          </h3>
                        </div>
                        <p className='mt-1 text-gray-600'>
                          {classItem.description}
                        </p>
                        <div className='space-y-1'>
                          <p className='flex items-center text-gray-600'>
                            <Users className='mr-2 h-4 w-4' />
                            Instructor: {classItem.teacher}
                          </p>
                          <p className='flex items-center text-gray-600'>
                            <Clock className='mr-2 h-4 w-4' />
                            Schedule: {classItem.schedule}
                          </p>
                          <p className='flex items-center text-gray-600'>
                            <Calendar className='mr-2 h-4 w-4' />
                            Duration: {classItem.startDate} -{' '}
                            {classItem.endDate}
                          </p>
                        </div>
                      </div>
                      {isEnrolled ? (
                        <div className='flex gap-2 self-start'>
                          <Badge
                            variant='default'
                            className='bg-green-100 px-3 py-1.5 text-green-700 hover:bg-green-100'
                          >
                            <CheckCircle className='mr-2 h-4 w-4' />
                            Enrolled
                          </Badge>
                          <Link href={`/modules/${classItem.id}`}>
                            <Button
                              size='sm'
                              className='bg-blue-600 hover:bg-blue-700'
                            >
                              <Play className='mr-2 h-4 w-4' />
                              Go to Course
                              <ArrowRight className='ml-2 h-4 w-4' />
                            </Button>
                          </Link>
                        </div>
                      ) : (
                        <Button
                          onClick={() => {
                            setSelectedClass(classItem);
                            setShowEnrollModal(true);
                          }}
                          className='self-start'
                        >
                          <Lock className='mr-2 h-4 w-4' />
                          Enroll Now
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
              {filteredClasses.length === 0 && (
                <div className='rounded-lg bg-gray-50/50 py-12 text-center'>
                  <BookOpen className='mx-auto mb-4 h-12 w-12 text-gray-400' />
                  <p className='text-lg text-gray-600'>
                    No available classes found
                  </p>
                  <p className='text-gray-500'>
                    Try adjusting your search criteria
                  </p>
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        {/* Enrollment Modal */}
        <Dialog
          open={showEnrollModal}
          onOpenChange={(open) => {
            setShowEnrollModal(open);
            if (!open) {
              setError('');
              setEnrollmentCode('');
            }
          }}
        >
          <DialogContent className='sm:max-w-md'>
            <DialogHeader>
              <DialogTitle className='text-xl'>
                Enroll in {selectedClass?.name}
              </DialogTitle>
              <p className='mt-1 text-sm text-gray-600'>
                Enter the enrollment code provided by your instructor
              </p>
            </DialogHeader>
            <div className='mt-4 space-y-4'>
              <div className='space-y-2 rounded-lg bg-gray-50 p-4'>
                <p className='flex items-center text-gray-700'>
                  <Users className='mr-2 h-4 w-4' />
                  Instructor: {selectedClass?.teacher}
                </p>
                <p className='flex items-center text-gray-700'>
                  <Clock className='mr-2 h-4 w-4' />
                  Schedule: {selectedClass?.schedule}
                </p>
              </div>
              <div className='space-y-1'>
                <div className='relative'>
                  <Key
                    className={`absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform ${
                      error ? 'text-red-500' : 'text-gray-400'
                    }`}
                  />
                  <Input
                    placeholder='Enter code (e.g., BIO101-2024)'
                    value={enrollmentCode}
                    onChange={(e) => setEnrollmentCode(e.target.value)}
                    className={`pl-10 ${error ? 'border-red-500 focus:border-red-500 focus:ring-red-500' : ''}`}
                  />
                </div>
                {error && <p className='text-sm text-red-600'>{error}</p>}
              </div>
              <Button 
                onClick={handleEnrollment} 
                className='w-full' 
                size='lg'
                disabled={enrolling}
              >
                {enrolling ? 'Enrolling...' : 'Complete Enrollment'}
              </Button>
            </div>
          </DialogContent>
        </Dialog>

        {/* Invitation Confirmation Modal */}
        <Dialog
          open={showInvitationModal}
          onOpenChange={(open) => {
            setShowInvitationModal(open);
            if (!open) {
              setSelectedInvitation(null);
            }
          }}
        >
          <DialogContent className='sm:max-w-md'>
            <DialogHeader>
              <DialogTitle className='text-xl'>
                Accept Class Invitation
              </DialogTitle>
              <p className='mt-1 text-sm text-gray-600'>
                Are you sure you want to join this class?
              </p>
            </DialogHeader>
            {selectedInvitation && (
              <div className='mt-4 space-y-4'>
                <div className='space-y-2 rounded-lg bg-gray-50 p-4'>
                  <h4 className='font-medium text-gray-900'>
                    {
                      mockClasses.find(
                        (c) => c.id === selectedInvitation.classId
                      )?.name
                    }
                  </h4>
                  <p className='flex items-center text-gray-700'>
                    <Users className='mr-2 h-4 w-4' />
                    Instructor: {selectedInvitation.invitedBy}
                  </p>
                  <p className='flex items-center text-gray-700'>
                    <Clock className='mr-2 h-4 w-4' />
                    Schedule:{' '}
                    {
                      mockClasses.find(
                        (c) => c.id === selectedInvitation.classId
                      )?.schedule
                    }
                  </p>
                </div>
                <div className='flex space-x-3'>
                  <Button
                    onClick={() => handleInvitationResponse(true)}
                    className='flex-1 bg-green-600 hover:bg-green-700'
                  >
                    <CheckCircle className='mr-2 h-4 w-4' />
                    Accept & Join Class
                  </Button>
                  <Button
                    variant='outline'
                    onClick={() => setShowInvitationModal(false)}
                    className='flex-1'
                  >
                    Cancel
                  </Button>
                </div>
              </div>
            )}
          </DialogContent>
        </Dialog>

        {/* Toast Notification */}
        {toast.show && (
          <div className='animate-in slide-in-from-bottom-2 fixed right-4 bottom-4 z-50'>
            <div
              className={`flex min-w-[300px] items-center space-x-3 rounded-lg px-6 py-4 shadow-lg ${
                toast.type === 'success'
                  ? 'bg-green-600 text-white'
                  : 'bg-red-600 text-white'
              } `}
            >
              {toast.type === 'success' ? (
                <CheckCircle2 className='h-5 w-5 flex-shrink-0' />
              ) : (
                <XCircle className='h-5 w-5 flex-shrink-0' />
              )}
              <p className='font-medium'>{toast.message}</p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ClassEnrollmentPage;
