import React from 'react';
import { ChevronDown, ChevronRight, Lock, CheckCircle2 } from 'lucide-react';
import { TreeNodeProps } from '@/types/lms';

export const TreeNode: React.FC<TreeNodeProps> = ({
  title,
  icon,
  isUnlocked,
  isCompleted = false,
  children,
  level,
  isExpanded = false,
  onToggle,
  onClick,
  hasChildren = false
}) => {
  return (
    <div className='select-none'>
      <button
        onClick={() => {
          if (hasChildren && onToggle) {
            onToggle();
          } else if (onClick) {
            onClick();
          }
        }}
        className={`flex w-full items-center space-x-2 rounded-lg p-2 text-left text-sm transition-colors ${
          isUnlocked
            ? 'text-blue-700 hover:bg-blue-50'
            : 'cursor-not-allowed text-gray-400'
        }`}
        style={{ paddingLeft: `${level * 16 + 8}px` }}
        disabled={!isUnlocked}
      >
        {hasChildren && (
          <span className='flex h-4 w-4 items-center justify-center'>
            {isExpanded ? (
              <ChevronDown className='h-3 w-3' />
            ) : (
              <ChevronRight className='h-3 w-3' />
            )}
          </span>
        )}
        <span className='flex h-4 w-4 items-center justify-center'>{icon}</span>
        <span className='flex-1 truncate'>{title}</span>
        {!isUnlocked && <Lock className='h-3 w-3' />}
        {isCompleted && <CheckCircle2 className='h-3 w-3 text-green-500' />}
      </button>
      {isExpanded && children && <div>{children}</div>}
    </div>
  );
};
