'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Save, Loader2 } from 'lucide-react';
import Link from 'next/link';
import { authStorage } from '@/lib/auth';
import { toast } from 'sonner';

interface ClassData {
  id: number;
  name: string;
  description: string;
  studentCount: number;
  courseCount: number;
  createdAt: string;
  status: string;
}

export default function EditClassPage() {
  const router = useRouter();
  const params = useParams();
  const classId = params.id as string;
  
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [classData, setClassData] = useState<ClassData | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    description: ''
  });

  useEffect(() => {
    if (classId) {
      fetchClassData();
    }
  }, [classId]);

  const fetchClassData = async () => {
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to edit classes');
        router.push('/auth/sign-in');
        return;
      }

      const response = await fetch(`/api/classes/${classId}?teacherId=${user.id}`);
      const data = await response.json();

      if (data.success && data.class) {
        setClassData(data.class);
        setFormData({
          name: data.class.name,
          description: data.class.description || ''
        });
      } else {
        toast.error(data.error || 'Failed to fetch class data');
        router.push('/dashboard/teacher/classes');
      }
    } catch (error) {
      console.error('Error fetching class:', error);
      toast.error('Failed to fetch class data');
      router.push('/dashboard/teacher/classes');
    } finally {
      setIsFetching(false);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to update classes');
        return;
      }

      if (!formData.name.trim()) {
        toast.error('Class name is required');
        return;
      }

      const response = await fetch(`/api/classes/${classId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: formData.name.trim(),
          description: formData.description.trim(),
          teacherId: user.id
        })
      });

      const data = await response.json();

      if (data.success) {
        toast.success('Class updated successfully!');
        router.push('/dashboard/teacher/classes');
      } else {
        toast.error(data.error || 'Failed to update class');
      }
    } catch (error) {
      console.error('Error updating class:', error);
      toast.error('Failed to update class');
    } finally {
      setIsLoading(false);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value
    }));
  };

  if (isFetching) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <Loader2 className='h-8 w-8 animate-spin' />
        <span className='ml-2'>Loading class data...</span>
      </div>
    );
  }

  if (!classData) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-center'>
          <h2 className='text-2xl font-bold'>Class not found</h2>
          <p className='text-muted-foreground mt-2'>The class you're looking for doesn't exist.</p>
          <Link href='/dashboard/teacher/classes'>
            <Button className='mt-4'>
              <ArrowLeft className='mr-2 h-4 w-4' />
              Back to Classes
            </Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center space-x-4'>
        <Link href='/dashboard/teacher/classes'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Edit Class
          </h1>
          <p className='text-muted-foreground'>
            Update the details for {classData.name}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Class Details</CardTitle>
          <CardDescription>
            Update the basic information for this class
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className='space-y-6'>
            <div className='space-y-4'>
              <div className='space-y-2'>
                <Label htmlFor='name'>Class Name</Label>
                <Input
                  id='name'
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder='e.g., Mathematics Grade 10A'
                  required
                />
                <p className='text-muted-foreground text-sm'>
                  Choose a descriptive name that includes subject and grade level
                </p>
              </div>

              <div className='space-y-2'>
                <Label htmlFor='description'>Description</Label>
                <Textarea
                  id='description'
                  value={formData.description}
                  onChange={(e) =>
                    handleInputChange('description', e.target.value)
                  }
                  placeholder='Brief description of the class and its objectives'
                  rows={4}
                />
                <p className='text-muted-foreground text-sm'>
                  Provide a brief description of what this class covers
                </p>
              </div>
            </div>

            <div className='flex justify-end space-x-4'>
              <Link href='/dashboard/teacher/classes'>
                <Button variant='outline' type='button'>
                  Cancel
                </Button>
              </Link>
              <Button type='submit' disabled={isLoading}>
                {isLoading ? (
                  <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                ) : (
                  <Save className='mr-2 h-4 w-4' />
                )}
                {isLoading ? 'Updating...' : 'Update Class'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Class Statistics Card */}
      <Card>
        <CardHeader>
          <CardTitle>Class Statistics</CardTitle>
          <CardDescription>Current statistics for this class</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
            <div className='text-center p-4 border rounded-lg'>
              <div className='text-2xl font-bold text-blue-600'>
                {classData.studentCount}
              </div>
              <div className='text-sm text-muted-foreground'>Students</div>
            </div>
            <div className='text-center p-4 border rounded-lg'>
              <div className='text-2xl font-bold text-green-600'>
                {classData.courseCount}
              </div>
              <div className='text-sm text-muted-foreground'>Courses</div>
            </div>
            <div className='text-center p-4 border rounded-lg'>
              <div className='text-2xl font-bold text-purple-600'>
                {new Date(classData.createdAt).toLocaleDateString()}
              </div>
              <div className='text-sm text-muted-foreground'>Created</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions Card */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
          <CardDescription>Manage students and courses for this class</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex flex-wrap gap-4'>
            <Link href={`/dashboard/teacher/classes/${classId}/students`}>
              <Button variant='outline'>
                Manage Students
              </Button>
            </Link>
            <Link href={`/dashboard/teacher/enrollments?classId=${classId}`}>
              <Button variant='outline'>
                Assign Courses
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}