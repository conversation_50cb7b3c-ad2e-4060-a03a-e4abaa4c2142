import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { classes, users } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';
import { authStorage } from '@/lib/auth';

// GET /api/classes - Get all classes for the current teacher
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // In a real app, you'd verify the JWT token here
    // For now, we'll get user info from the request or session
    const searchParams = request.nextUrl.searchParams;
    const teacherId = searchParams.get('teacherId');
    
    if (!teacherId) {
      return NextResponse.json({ error: 'Teacher ID required' }, { status: 400 });
    }

    // Get classes for the teacher
    const teacherClasses = await db
      .select({
        id: classes.id,
        name: classes.name,
        description: classes.description,
        institutionId: classes.institutionId,
        teacherId: classes.teacherId,
        coverPicture: classes.coverPicture,
        createdAt: classes.createdAt,
        updatedAt: classes.updatedAt
      })
      .from(classes)
      .where(eq(classes.teacherId, parseInt(teacherId)));

    // Get student count for each class (this would require a join with student enrollments)
    // For now, we'll return the classes without student count
    const classesWithCounts = teacherClasses.map(cls => ({
      ...cls,
      studentCount: 0, // TODO: Calculate actual student count
      courseCount: 0   // TODO: Calculate actual course count
    }));

    return NextResponse.json({ classes: classesWithCounts });
  } catch (error) {
    console.error('Error fetching classes:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/classes - Create a new class
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { name, description, teacherId, institutionId, coverPicture } = body;

    // Validate required fields
    if (!name || !teacherId || !institutionId) {
      return NextResponse.json(
        { error: 'Name, teacher ID, and institution ID are required' },
        { status: 400 }
      );
    }

    // Verify teacher exists and belongs to the institution
    const teacher = await db
      .select()
      .from(users)
      .where(
        and(
          eq(users.id, teacherId),
          eq(users.institutionId, institutionId),
          eq(users.role, 'teacher')
        )
      )
      .limit(1);

    if (teacher.length === 0) {
      return NextResponse.json(
        { error: 'Teacher not found or not authorized' },
        { status: 403 }
      );
    }

    // Create the class
    const newClass = await db
      .insert(classes)
      .values({
        name,
        description,
        teacherId,
        institutionId,
        coverPicture
      })
      .returning();

    return NextResponse.json(
      { class: newClass[0], message: 'Class created successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating class:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}