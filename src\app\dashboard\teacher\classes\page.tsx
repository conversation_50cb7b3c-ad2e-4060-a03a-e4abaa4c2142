'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Users,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  BookOpen,
  Loader2
} from 'lucide-react';
import Link from 'next/link';
import { authStorage } from '@/lib/auth';
import { toast } from 'sonner';

interface ClassData {
  id: number;
  name: string;
  description: string;
  studentCount: number;
  courseCount: number;
  createdAt: string;
  status: string;
}

export default function ClassesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [classes, setClasses] = useState<ClassData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDeleting, setIsDeleting] = useState<number | null>(null);

  useEffect(() => {
    fetchClasses();
  }, []);

  const fetchClasses = async () => {
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to view classes');
        return;
      }

      const response = await fetch(`/api/classes?teacherId=${user.id}`);
      const data = await response.json();

      if (data.success) {
        setClasses(data.classes || []);
      } else {
        toast.error(data.error || 'Failed to fetch classes');
      }
    } catch (error) {
      console.error('Error fetching classes:', error);
      toast.error('Failed to fetch classes');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteClass = async (classId: number) => {
    if (!confirm('Are you sure you want to delete this class? This action cannot be undone.')) {
      return;
    }

    setIsDeleting(classId);
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to delete classes');
        return;
      }

      const response = await fetch(`/api/classes/${classId}?teacherId=${user.id}`, {
        method: 'DELETE'
      });
      const data = await response.json();

      if (data.success) {
        toast.success('Class deleted successfully');
        fetchClasses(); // Refresh the list
      } else {
        toast.error(data.error || 'Failed to delete class');
      }
    } catch (error) {
      console.error('Error deleting class:', error);
      toast.error('Failed to delete class');
    } finally {
      setIsDeleting(null);
    }
  };

  const filteredClasses = classes.filter(
    (classItem) =>
      classItem.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      classItem.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>My Classes</h1>
          <p className='text-muted-foreground'>
            Manage your classes and student groups
          </p>
        </div>
        <Link href='/dashboard/teacher/classes/new'>
          <Button>
            <Plus className='mr-2 h-4 w-4' />
            Create Class
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Classes</CardTitle>
          <CardDescription>View and manage all your classes</CardDescription>
        </CardHeader>
        <CardContent>
          <div className='mb-4 flex items-center space-x-2'>
            <div className='relative flex-1'>
              <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
              <Input
                placeholder='Search classes...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-8'
              />
            </div>
          </div>

          {isLoading ? (
            <div className='flex items-center justify-center py-8'>
              <Loader2 className='h-8 w-8 animate-spin' />
              <span className='ml-2'>Loading classes...</span>
            </div>
          ) : (
            <div className='rounded-md border'>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Class Name</TableHead>
                    <TableHead>Students</TableHead>
                    <TableHead>Courses</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Created</TableHead>
                    <TableHead className='w-[70px]'>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredClasses.map((classItem) => (
                    <TableRow key={classItem.id}>
                      <TableCell>
                        <div className='space-y-1'>
                          <p className='font-medium'>{classItem.name}</p>
                          <p className='text-muted-foreground text-sm'>
                            {classItem.description}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='flex items-center space-x-1'>
                          <Users className='text-muted-foreground h-4 w-4' />
                          <span>{classItem.studentCount}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='flex items-center space-x-1'>
                          <BookOpen className='text-muted-foreground h-4 w-4' />
                          <span>{classItem.courseCount}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            classItem.status === 'active'
                              ? 'default'
                              : 'secondary'
                          }
                        >
                          {classItem.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <span className='text-sm'>
                          {new Date(classItem.createdAt).toLocaleDateString()}
                        </span>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant='ghost' className='h-8 w-8 p-0'>
                              <MoreHorizontal className='h-4 w-4' />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align='end'>
                            <DropdownMenuItem asChild>
                              <Link
                                href={`/dashboard/teacher/classes/${classItem.id}`}
                              >
                                <Edit className='mr-2 h-4 w-4' />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link
                                href={`/dashboard/teacher/classes/${classItem.id}/students`}
                              >
                                <Users className='mr-2 h-4 w-4' />
                                Manage Students
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              className='text-red-600'
                              onClick={() => handleDeleteClass(classItem.id)}
                              disabled={isDeleting === classItem.id}
                            >
                              {isDeleting === classItem.id ? (
                                <Loader2 className='mr-2 h-4 w-4 animate-spin' />
                              ) : (
                                <Trash2 className='mr-2 h-4 w-4' />
                              )}
                              {isDeleting === classItem.id ? 'Deleting...' : 'Delete'}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {!isLoading && filteredClasses.length === 0 && (
            <div className='py-8 text-center'>
              <Users className='text-muted-foreground mx-auto h-12 w-12' />
              <h3 className='mt-2 text-sm font-semibold'>No classes found</h3>
              <p className='text-muted-foreground mt-1 text-sm'>
                {searchTerm
                  ? 'Try adjusting your search terms.'
                  : 'Get started by creating a new class.'}
              </p>
              {!searchTerm && (
                <div className='mt-6'>
                  <Link href='/dashboard/teacher/classes/new'>
                    <Button>
                      <Plus className='mr-2 h-4 w-4' />
                      Create Class
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
