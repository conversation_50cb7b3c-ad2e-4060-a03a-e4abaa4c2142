'use client';

import React, { createContext, useContext, useState, ReactNode } from 'react';
import { architectureCourse } from '@/constants/shared-course-data';
import { Course } from '@/types/lms';

interface EnrollmentContextType {
  isEnrolled: boolean;
  courseData: Course;
  enrollInCourse: () => void;
  updateCourseProgress: (updatedCourse: Course) => void;
}

const EnrollmentContext = createContext<EnrollmentContextType | undefined>(
  undefined
);

export const useEnrollment = () => {
  const context = useContext(EnrollmentContext);
  if (!context) {
    throw new Error('useEnrollment must be used within an EnrollmentProvider');
  }
  return context;
};

interface EnrollmentProviderProps {
  children: ReactNode;
}

export const EnrollmentProvider: React.FC<EnrollmentProviderProps> = ({
  children
}) => {
  const [isEnrolled, setIsEnrolled] = useState(false);
  const [courseData, setCourseData] = useState<Course>(architectureCourse);

  const enrollInCourse = () => {
    setIsEnrolled(true);
    // Update course status when enrolled
    setCourseData((prev) => ({
      ...prev,
      status: 'in-progress'
    }));
  };

  const updateCourseProgress = (updatedCourse: Course) => {
    setCourseData(updatedCourse);
  };

  const value = {
    isEnrolled,
    courseData,
    enrollInCourse,
    updateCourseProgress
  };

  return (
    <EnrollmentContext.Provider value={value}>
      {children}
    </EnrollmentContext.Provider>
  );
};
