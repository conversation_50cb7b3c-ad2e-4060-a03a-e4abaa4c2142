'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { authStorage } from '@/lib/auth';
import { Loader2 } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Save, Plus, Trash2 } from 'lucide-react';
import Link from 'next/link';

interface Question {
  id: string;
  type: 'multiple_choice' | 'true_false' | 'essay';
  question: string;
  options?: string[];
  correctAnswer: string;
  points: number;
}

interface Course {
  id: number;
  title: string;
  description: string;
}

interface Chapter {
  id: number;
  title: string;
  moduleId: number;
  orderIndex: number;
}

interface Module {
  id: number;
  title: string;
  courseId: number;
  chapters: Chapter[];
}

export default function NewQuizPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingData, setIsLoadingData] = useState(true);
  const [courses, setCourses] = useState<Course[]>([]);
  const [modules, setModules] = useState<Module[]>([]);
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    courseId: '',
    chapterId: '',
    moduleId: '',
    timeLimit: 30,
    passingScore: 70,
    maxAttempts: 3,
    isActive: true
  });
  const [questions, setQuestions] = useState<Question[]>([]);

  // Fetch courses data
  const fetchCourses = async () => {
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to continue');
        router.push('/login');
        return;
      }

      const response = await fetch(`/api/courses?teacherId=${user.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch courses');
      }
      const data = await response.json();
      setCourses(data);
    } catch (error) {
      console.error('Error fetching courses:', error);
      toast.error('Failed to load courses');
    }
  };

  // Fetch modules for selected course
  const fetchModules = async (courseId: string) => {
    try {
      const response = await fetch(`/api/modules?courseId=${courseId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch modules');
      }
      const data = await response.json();
      setModules(data);
    } catch (error) {
      console.error('Error fetching modules:', error);
      toast.error('Failed to load modules');
    }
  };

  // Fetch chapters for selected module
  const fetchChapters = async (moduleId: string) => {
    try {
      const response = await fetch(`/api/chapters?moduleId=${moduleId}`);
      if (!response.ok) {
        throw new Error('Failed to fetch chapters');
      }
      const data = await response.json();
      setChapters(data);
    } catch (error) {
      console.error('Error fetching chapters:', error);
      toast.error('Failed to load chapters');
    }
  };

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      setIsLoadingData(true);
      await fetchCourses();
      setIsLoadingData(false);
    };
    loadData();
  }, []);

  // Load modules when course changes
  useEffect(() => {
    if (formData.courseId) {
      fetchModules(formData.courseId);
      setFormData(prev => ({ ...prev, moduleId: '', chapterId: '' }));
      setChapters([]);
    }
  }, [formData.courseId]);

  // Load chapters when module changes
  useEffect(() => {
    if (formData.moduleId) {
      fetchChapters(formData.moduleId);
      setFormData(prev => ({ ...prev, chapterId: '' }));
    }
  }, [formData.moduleId]);

  const addQuestion = () => {
    const newQuestion: Question = {
      id: Date.now().toString(),
      type: 'multiple_choice',
      question: '',
      options: ['', '', '', ''],
      correctAnswer: '',
      points: 1
    };
    setQuestions([...questions, newQuestion]);
  };

  const updateQuestion = (id: string, field: string, value: any) => {
    setQuestions(
      questions.map((q) => (q.id === id ? { ...q, [field]: value } : q))
    );
  };

  const removeQuestion = (id: string) => {
    setQuestions(questions.filter((q) => q.id !== id));
  };

  const updateQuestionOption = (
    questionId: string,
    optionIndex: number,
    value: string
  ) => {
    setQuestions(
      questions.map((q) =>
        q.id === questionId
          ? {
              ...q,
              options: q.options?.map((opt, idx) =>
                idx === optionIndex ? value : opt
              )
            }
          : q
      )
    );
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to continue');
        router.push('/login');
        return;
      }

      // Prepare quiz data
      const quizData = {
        title: formData.title,
        description: formData.description,
        courseId: parseInt(formData.courseId),
        chapterId: formData.chapterId ? parseInt(formData.chapterId) : null,
        timeLimit: formData.timeLimit,
        passingScore: formData.passingScore,
        maxAttempts: formData.maxAttempts,
        isActive: formData.isActive,
        teacherId: user.id,
        questions: questions.map(q => ({
          type: q.type,
          question: q.question,
          options: q.options || [],
          correctAnswer: q.correctAnswer,
          points: q.points
        }))
      };

      const response = await fetch('/api/quizzes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(quizData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create quiz');
      }

      toast.success('Quiz created successfully!');
      router.push('/dashboard/teacher/quizzes');
    } catch (error) {
      console.error('Error creating quiz:', error);
      toast.error(error instanceof Error ? error.message : 'Failed to create quiz');
    } finally {
      setIsLoading(false);
    }
  };

  const renderQuestionForm = (question: Question, index: number) => (
    <Card key={question.id}>
      <CardHeader>
        <div className='flex items-center justify-between'>
          <CardTitle className='text-lg'>Question {index + 1}</CardTitle>
          <Button
            variant='outline'
            size='sm'
            onClick={() => removeQuestion(question.id)}
          >
            <Trash2 className='h-4 w-4' />
          </Button>
        </div>
      </CardHeader>
      <CardContent className='space-y-4'>
        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
          <div className='space-y-2'>
            <Label>Question Type</Label>
            <Select
              value={question.type}
              onValueChange={(value: any) =>
                updateQuestion(question.id, 'type', value)
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='multiple_choice'>Multiple Choice</SelectItem>
                <SelectItem value='true_false'>True/False</SelectItem>
                <SelectItem value='essay'>Essay</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className='space-y-2'>
            <Label>Points</Label>
            <Input
              type='number'
              value={question.points}
              onChange={(e) =>
                updateQuestion(
                  question.id,
                  'points',
                  parseInt(e.target.value) || 1
                )
              }
              min='1'
              max='10'
            />
          </div>
        </div>

        <div className='space-y-2'>
          <Label>Question</Label>
          <Textarea
            value={question.question}
            onChange={(e) =>
              updateQuestion(question.id, 'question', e.target.value)
            }
            placeholder='Enter your question here...'
            rows={3}
          />
        </div>

        {question.type === 'multiple_choice' && (
          <div className='space-y-2'>
            <Label>Answer Options</Label>
            <div className='space-y-2'>
              {question.options?.map((option, optionIndex) => (
                <div key={optionIndex} className='flex items-center space-x-2'>
                  <Input
                    value={option}
                    onChange={(e) =>
                      updateQuestionOption(
                        question.id,
                        optionIndex,
                        e.target.value
                      )
                    }
                    placeholder={`Option ${String.fromCharCode(65 + optionIndex)}`}
                  />
                  <input
                    type='radio'
                    name={`correct-${question.id}`}
                    checked={question.correctAnswer === option}
                    onChange={() =>
                      updateQuestion(question.id, 'correctAnswer', option)
                    }
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {question.type === 'true_false' && (
          <div className='space-y-2'>
            <Label>Correct Answer</Label>
            <Select
              value={question.correctAnswer}
              onValueChange={(value) =>
                updateQuestion(question.id, 'correctAnswer', value)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder='Select correct answer' />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value='true'>True</SelectItem>
                <SelectItem value='false'>False</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}

        {question.type === 'essay' && (
          <div className='space-y-2'>
            <Label>Sample Answer / Key Points</Label>
            <Textarea
              value={question.correctAnswer}
              onChange={(e) =>
                updateQuestion(question.id, 'correctAnswer', e.target.value)
              }
              placeholder='Provide a sample answer or key points for grading...'
              rows={3}
            />
          </div>
        )}
      </CardContent>
    </Card>
  );

  if (isLoadingData) {
    return (
      <div className='flex items-center justify-center min-h-[400px]'>
        <div className='text-center'>
          <Loader2 className='h-8 w-8 animate-spin mx-auto mb-4' />
          <p className='text-muted-foreground'>Loading course data...</p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center space-x-4'>
        <Link href='/dashboard/teacher/quizzes'>
          <Button variant='outline' size='sm'>
            <ArrowLeft className='mr-2 h-4 w-4' />
            Back
          </Button>
        </Link>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>Create New Quiz</h1>
          <p className='text-muted-foreground'>
            Create a new quiz or assessment for your course
          </p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className='space-y-6'>
        {/* Quiz Details */}
        <Card>
          <CardHeader>
            <CardTitle>Quiz Details</CardTitle>
            <CardDescription>Basic information about your quiz</CardDescription>
          </CardHeader>
          <CardContent className='space-y-4'>
            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div className='space-y-2'>
                <Label htmlFor='title'>Quiz Title</Label>
                <Input
                  id='title'
                  value={formData.title}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, title: e.target.value }))
                  }
                  placeholder='e.g., Module 1 Assessment'
                  required
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='course'>Course</Label>
                <Select
                  value={formData.courseId}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, courseId: value }))
                  }
                  required
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select course' />
                  </SelectTrigger>
                  <SelectContent>
                    {courses.map((course) => (
                      <SelectItem key={course.id} value={course.id.toString()}>
                        {course.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className='space-y-2'>
              <Label htmlFor='description'>Description</Label>
              <Textarea
                id='description'
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value
                  }))
                }
                placeholder='Brief description of the quiz content'
                rows={3}
              />
            </div>

            <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
              <div className='space-y-2'>
                <Label htmlFor='module'>Module (Optional)</Label>
                <Select
                  value={formData.moduleId}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, moduleId: value }))
                  }
                  disabled={!formData.courseId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select module' />
                  </SelectTrigger>
                  <SelectContent>
                    {modules.map((module) => (
                      <SelectItem key={module.id} value={module.id.toString()}>
                        {module.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className='space-y-2'>
                <Label htmlFor='chapter'>Chapter (Optional)</Label>
                <Select
                  value={formData.chapterId}
                  onValueChange={(value) =>
                    setFormData((prev) => ({ ...prev, chapterId: value }))
                  }
                  disabled={!formData.moduleId}
                >
                  <SelectTrigger>
                    <SelectValue placeholder='Select chapter' />
                  </SelectTrigger>
                  <SelectContent>
                    {chapters.map((chapter) => (
                      <SelectItem key={chapter.id} value={chapter.id.toString()}>
                        {chapter.title}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className='grid grid-cols-1 gap-4 md:grid-cols-3'>
              <div className='space-y-2'>
                <Label htmlFor='timeLimit'>Time Limit (minutes)</Label>
                <Input
                  id='timeLimit'
                  type='number'
                  value={formData.timeLimit}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      timeLimit: parseInt(e.target.value) || 30
                    }))
                  }
                  min='5'
                  max='180'
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='passingScore'>Passing Score (%)</Label>
                <Input
                  id='passingScore'
                  type='number'
                  value={formData.passingScore}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      passingScore: parseInt(e.target.value) || 70
                    }))
                  }
                  min='0'
                  max='100'
                />
              </div>
              <div className='space-y-2'>
                <Label htmlFor='maxAttempts'>Max Attempts</Label>
                <Input
                  id='maxAttempts'
                  type='number'
                  value={formData.maxAttempts}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      maxAttempts: parseInt(e.target.value) || 3
                    }))
                  }
                  min='1'
                  max='10'
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Separator />

        {/* Questions Section */}
        <div className='space-y-4'>
          <div className='flex items-center justify-between'>
            <div>
              <h2 className='text-2xl font-bold'>Questions</h2>
              <p className='text-muted-foreground'>
                Add questions to your quiz
              </p>
            </div>
            <Button type='button' onClick={addQuestion}>
              <Plus className='mr-2 h-4 w-4' />
              Add Question
            </Button>
          </div>

          {questions.length === 0 ? (
            <Card>
              <CardContent className='pt-6'>
                <div className='py-8 text-center'>
                  <p className='text-muted-foreground'>
                    No questions added yet.
                  </p>
                  <Button type='button' onClick={addQuestion} className='mt-4'>
                    <Plus className='mr-2 h-4 w-4' />
                    Add Your First Question
                  </Button>
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className='space-y-4'>
              {questions.map((question, index) =>
                renderQuestionForm(question, index)
              )}
            </div>
          )}
        </div>

        {/* Submit Button */}
        <div className='flex justify-end space-x-4'>
          <Link href='/dashboard/teacher/quizzes'>
            <Button variant='outline' type='button'>
              Cancel
            </Button>
          </Link>
          <Button type='submit' disabled={isLoading || questions.length === 0}>
            <Save className='mr-2 h-4 w-4' />
            {isLoading ? 'Creating...' : 'Create Quiz'}
          </Button>
        </div>
      </form>
    </div>
  );
}
