'use client';

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { BookO<PERSON>, User, Clock, Play } from 'lucide-react';
import Link from 'next/link';
import { useEnrollment } from '@/contexts/enrollment-context';

const ModulesPage: React.FC = () => {
  const { isEnrolled, courseData } = useEnrollment();

  if (!isEnrolled) {
    return (
      <div className='min-h-screen bg-gray-50 p-8'>
        <div className='mx-auto max-w-6xl'>
          <div className='py-16 text-center'>
            <BookOpen className='mx-auto mb-6 h-16 w-16 text-gray-400' />
            <h1 className='mb-4 text-3xl font-bold text-gray-900'>
              My Learning Modules
            </h1>
            <p className='mb-8 text-gray-600'>
              You haven&apos;t enrolled in any courses yet.
            </p>
            <p className='text-gray-500'>
              Visit the Enroll page to join available courses.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Mock multiple courses for demonstration
  const courses = [courseData];

  return (
    <div className='min-h-screen bg-gray-50 p-8'>
      <div className='mx-auto max-w-6xl space-y-8'>
        {/* Header */}
        <div className='text-center'>
          <h1 className='mb-2 text-3xl font-bold text-gray-900'>
            My Learning Modules
          </h1>
          <p className='text-gray-600'>Continue your learning journey</p>
        </div>

        {/* Course Cards Grid */}
        <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
          {courses.map((course) => {
            const completedChapters = course.modules.reduce(
              (total, module) =>
                total +
                module.chapters.filter(
                  (ch) =>
                    ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
                ).length,
              0
            );

            const totalChapters = course.modules.reduce(
              (total, module) => total + module.chapters.length,
              0
            );
            const overallProgress =
              totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;

            const completedModules = course.modules.filter(
              (m) =>
                m.chapters.every(
                  (ch) =>
                    ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
                ) && m.moduleQuiz.isPassed
            ).length;

            return (
              <Card
                key={course.id}
                className='group transition-shadow hover:shadow-lg'
              >
                <CardHeader className='border-b'>
                  <div className='space-y-3'>
                    <div className='flex items-start justify-between'>
                      <CardTitle className='text-lg transition-colors group-hover:text-blue-600'>
                        {course.name}
                      </CardTitle>
                      <Badge
                        variant={
                          course.status === 'completed'
                            ? 'default'
                            : 'secondary'
                        }
                      >
                        {course.status === 'completed'
                          ? 'Completed'
                          : 'In Progress'}
                      </Badge>
                    </div>
                    <p className='line-clamp-2 text-sm text-gray-600'>
                      {course.description}
                    </p>
                    <div className='flex flex-wrap gap-4 text-xs text-gray-500'>
                      <div className='flex items-center gap-1'>
                        <User className='h-3 w-3' />
                        <span>{course.instructor}</span>
                      </div>
                      <div className='flex items-center gap-1'>
                        <Clock className='h-3 w-3' />
                        <span>{course.modules.length} Modules</span>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className='pt-4'>
                  <div className='space-y-4'>
                    {/* Progress */}
                    <div>
                      <div className='mb-2 flex items-center justify-between'>
                        <span className='text-sm font-medium'>Progress</span>
                        <span className='text-sm text-gray-500'>
                          {Math.round(overallProgress)}%
                        </span>
                      </div>
                      <Progress value={overallProgress} className='h-2' />
                    </div>

                    {/* Quick Stats */}
                    <div className='grid grid-cols-2 gap-3 text-center'>
                      <div>
                        <div className='text-lg font-bold text-blue-600'>
                          {completedModules}
                        </div>
                        <div className='text-xs text-gray-500'>Modules</div>
                      </div>
                      <div>
                        <div className='text-lg font-bold text-green-600'>
                          {completedChapters}
                        </div>
                        <div className='text-xs text-gray-500'>Chapters</div>
                      </div>
                    </div>

                    {/* Action Button */}
                    <Link href={`/modules/${course.id}`} className='block'>
                      <Button className='w-full' size='sm'>
                        <Play className='mr-2 h-4 w-4' />
                        Continue Learning
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            );
          })}

          {/* Add more course placeholder cards */}
          {[...Array(5)].map((_, index) => (
            <Card
              key={`placeholder-${index}`}
              className='border-dashed opacity-50'
            >
              <CardHeader className='border-b border-dashed'>
                <div className='space-y-3'>
                  <div className='flex items-start justify-between'>
                    <CardTitle className='text-lg text-gray-400'>
                      Course {index + 2}
                    </CardTitle>
                    <Badge variant='outline'>Coming Soon</Badge>
                  </div>
                  <p className='text-sm text-gray-400'>
                    More courses will be available soon.
                  </p>
                </div>
              </CardHeader>
              <CardContent className='pt-4'>
                <div className='space-y-4'>
                  <div className='h-12 animate-pulse rounded bg-gray-100'></div>
                  <div className='grid grid-cols-2 gap-3'>
                    <div className='h-8 animate-pulse rounded bg-gray-100'></div>
                    <div className='h-8 animate-pulse rounded bg-gray-100'></div>
                  </div>
                  <Button className='w-full' size='sm' disabled>
                    <BookOpen className='mr-2 h-4 w-4' />
                    Not Available
                  </Button>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ModulesPage;
