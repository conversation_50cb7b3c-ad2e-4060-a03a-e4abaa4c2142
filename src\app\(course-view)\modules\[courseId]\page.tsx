'use client';

import React, { useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle
} from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import {
  BookOpen,
  BarChart3,
  Trophy,
  Award,
  Calendar,
  Download,
  Building,
  ArrowLeft
} from 'lucide-react';
import Link from 'next/link';
import { useParams } from 'next/navigation';

import { Course, Quiz } from '@/types/lms';
import { useEnrollment } from '@/contexts/enrollment-context';
import {
  QuizModal,
  CertificateTemplate,
  CourseTab,
  ProgressTab,
  ExamTab,
  CertificateTab
} from '@/components/lms';

const CoursePage: React.FC = () => {
  const params = useParams();
  const courseId = params.courseId as string;
  const { courseData, updateCourseProgress } = useEnrollment();

  const [expandedContents, setExpandedContents] = useState<{
    [key: string]: boolean;
  }>({});
  const [expandedModules, setExpandedModules] = useState<{
    [key: string]: boolean;
  }>({});
  const [expandedChapters, setExpandedChapters] = useState<{
    [key: string]: boolean;
  }>({});
  const [currentQuiz, setCurrentQuiz] = useState<Quiz | null>(null);
  const [showCertificate, setShowCertificate] = useState(false);
  const [activeTab, setActiveTab] = useState('course');

  const toggleContent = useCallback((contentId: string) => {
    setExpandedContents((prev) => ({ ...prev, [contentId]: !prev[contentId] }));
  }, []);

  const toggleModule = useCallback((moduleId: string) => {
    setExpandedModules((prev) => ({ ...prev, [moduleId]: !prev[moduleId] }));
  }, []);

  const toggleChapter = useCallback((chapterId: string) => {
    setExpandedChapters((prev) => ({ ...prev, [chapterId]: !prev[chapterId] }));
  }, []);

  const expandAllModules = useCallback(() => {
    const newExpandedModules: { [key: string]: boolean } = {};
    courseData.modules.forEach((module) => {
      if (module.isUnlocked) {
        newExpandedModules[module.id] = true;
      }
    });
    setExpandedModules(newExpandedModules);
  }, [courseData.modules]);

  const collapseAllModules = useCallback(() => {
    setExpandedModules({});
  }, []);

  const expandAllChaptersInModule = useCallback(
    (moduleId: string) => {
      const courseModule = courseData.modules.find((m) => m.id === moduleId);
      if (!courseModule) return;

      const newExpandedChapters = { ...expandedChapters };
      courseModule.chapters.forEach((chapter) => {
        if (chapter.isUnlocked) {
          newExpandedChapters[chapter.id] = true;
        }
      });
      setExpandedChapters(newExpandedChapters);
    },
    [courseData.modules, expandedChapters]
  );

  const collapseAllChaptersInModule = useCallback(
    (moduleId: string) => {
      const courseModule = courseData.modules.find((m) => m.id === moduleId);
      if (!courseModule) return;

      setExpandedModules((prev) => ({ ...prev, [moduleId]: false }));

      const newExpandedContents = { ...expandedContents };
      const newExpandedChapters = { ...expandedChapters };
      courseModule.chapters.forEach((chapter) => {
        delete newExpandedChapters[chapter.id];
      });
      setExpandedChapters(newExpandedChapters);
    },
    [courseData.modules, expandedChapters]
  );

  const toggleContentComplete = useCallback(
    (contentId: string) => {
      const newCourse = JSON.parse(JSON.stringify(courseData)) as Course;

      let contentFound = false;
      for (const courseModule of newCourse.modules) {
        for (const chapter of courseModule.chapters) {
          const content = chapter.contents.find((c) => c.id === contentId);
          if (content) {
            content.isCompleted = !content.isCompleted;
            contentFound = true;

            const completedContents = chapter.contents.filter(
              (c) => c.isCompleted
            ).length;
            const nextChapterIndex = chapter.order;
            const nextChapter = courseModule.chapters.find(
              (ch) => ch.order === nextChapterIndex + 1
            );

            if (
              nextChapter &&
              completedContents === chapter.contents.length &&
              chapter.quiz.isPassed
            ) {
              nextChapter.isUnlocked = true;
            }

            const allChaptersCompleted = courseModule.chapters.every(
              (ch) =>
                ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
            );

            if (allChaptersCompleted && courseModule.moduleQuiz.isPassed) {
              const nextModuleIndex = courseModule.order;
              const nextModule = newCourse.modules.find(
                (m) => m.order === nextModuleIndex + 1
              );
              if (nextModule) {
                nextModule.isUnlocked = true;
                if (nextModule.chapters.length > 0) {
                  nextModule.chapters[0].isUnlocked = true;
                }
              }
            }

            break;
          }
        }
        if (contentFound) break;
      }

      updateCourseProgress(newCourse);
    },
    [courseData, updateCourseProgress]
  );

  const startQuiz = useCallback(
    (quizId: string) => {
      let quiz: Quiz | undefined;

      for (const courseModule of courseData.modules) {
        for (const chapter of courseModule.chapters) {
          if (chapter.quiz.id === quizId) {
            quiz = chapter.quiz;
            break;
          }
        }
        if (courseModule.moduleQuiz.id === quizId) {
          quiz = courseModule.moduleQuiz;
          break;
        }
      }

      if (courseData.finalExam.id === quizId) {
        quiz = courseData.finalExam;
      }

      if (quiz) {
        setCurrentQuiz({ ...quiz });
      }
    },
    [courseData]
  );

  const handleQuizComplete = useCallback(
    (score: number) => {
      if (!currentQuiz) return;

      const newCourse = JSON.parse(JSON.stringify(courseData)) as Course;

      const updateQuiz = (quiz: Quiz) => {
        quiz.attempts += 1;
        quiz.lastScore = score;
        quiz.isPassed = score >= quiz.minimumScore;
      };

      for (const courseModule of newCourse.modules) {
        for (const chapter of courseModule.chapters) {
          if (chapter.quiz.id === currentQuiz.id) {
            updateQuiz(chapter.quiz);

            const allContentsCompleted = chapter.contents.every(
              (c) => c.isCompleted
            );
            if (chapter.quiz.isPassed && allContentsCompleted) {
              const nextChapter = courseModule.chapters.find(
                (ch) => ch.order === chapter.order + 1
              );
              if (nextChapter) {
                nextChapter.isUnlocked = true;
              }
            }
            break;
          }
        }

        if (courseModule.moduleQuiz.id === currentQuiz.id) {
          updateQuiz(courseModule.moduleQuiz);

          const allChaptersCompleted = courseModule.chapters.every(
            (ch) => ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
          );
          if (courseModule.moduleQuiz.isPassed && allChaptersCompleted) {
            const nextModule = newCourse.modules.find(
              (m) => m.order === courseModule.order + 1
            );
            if (nextModule) {
              nextModule.isUnlocked = true;
              if (nextModule.chapters.length > 0) {
                nextModule.chapters[0].isUnlocked = true;
              }
            }
          }
        }
      }

      if (newCourse.finalExam.id === currentQuiz.id) {
        updateQuiz(newCourse.finalExam);

        const allModulesCompleted = newCourse.modules.every(
          (m) =>
            m.chapters.every(
              (ch) =>
                ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
            ) && m.moduleQuiz.isPassed
        );

        if (newCourse.finalExam.isPassed && allModulesCompleted) {
          newCourse.certificate.isEligible = true;
          newCourse.certificate.completionDate = new Date()
            .toISOString()
            .split('T')[0];
          newCourse.status = 'completed';
        }
      }

      updateCourseProgress(newCourse);
      setCurrentQuiz(null);
    },
    [currentQuiz, courseData, updateCourseProgress]
  );

  const generateCertificate = useCallback(() => {
    if (courseData.certificate.isEligible) {
      const updatedCourse = {
        ...courseData,
        certificate: {
          ...courseData.certificate,
          isGenerated: true,
          certificateUrl: `#certificate-${courseData.id}`
        }
      };
      updateCourseProgress(updatedCourse);
      setShowCertificate(true);
    }
  }, [courseData, updateCourseProgress]);

  const handleNavigateToSection = useCallback(
    (moduleId: string, chapterId?: string) => {
      setExpandedModules((prev) => ({ ...prev, [moduleId]: true }));
      if (chapterId) {
        setExpandedChapters((prev) => ({ ...prev, [chapterId]: true }));
      }
      setActiveTab('course');
    },
    []
  );

  const completedChapters = courseData.modules.reduce(
    (total, module) =>
      total +
      module.chapters.filter(
        (ch) => ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
      ).length,
    0
  );

  const totalChapters = courseData.modules.reduce(
    (total, module) => total + module.chapters.length,
    0
  );
  const overallProgress =
    totalChapters > 0 ? (completedChapters / totalChapters) * 100 : 0;

  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='mx-auto max-w-full space-y-6 p-8'>
        {/* Header with Back Button */}
        <div className='flex items-center justify-between'>
          <div className='flex items-center space-x-4'>
            <Link href='/modules'>
              <Button
                variant='outline'
                size='sm'
                className='flex items-center space-x-2'
              >
                <ArrowLeft className='h-4 w-4' />
                <span>Back to Modules</span>
              </Button>
            </Link>
            <div className='flex items-center space-x-3'>
              <Building className='h-8 w-8 text-blue-600' />
              <div>
                <h1 className='text-3xl font-bold text-gray-900'>
                  {courseData.name}
                </h1>
                <p className='text-gray-600'>Course Code: {courseData.code}</p>
                <p className='text-gray-600'>
                  Instructor: {courseData.instructor}
                </p>
                <div className='mt-2 flex items-center space-x-4'>
                  <span className='text-sm text-gray-500'>
                    <Calendar className='mr-1 inline h-4 w-4' />
                    {courseData.startDate} - {courseData.endDate}
                  </span>
                  <Badge
                    variant={
                      courseData.status === 'completed'
                        ? 'default'
                        : 'secondary'
                    }
                  >
                    {courseData.status === 'completed'
                      ? 'Completed'
                      : 'In Progress'}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
          <div className='flex items-center space-x-4'>
            <div className='text-right'>
              <p className='text-sm text-gray-500'>Overall Progress</p>
              <div className='flex items-center space-x-2'>
                <Progress value={overallProgress} className='w-32' />
                <span className='text-sm font-medium'>
                  {Math.round(overallProgress)}%
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className='mb-6'>
          <TabsList className='grid w-full grid-cols-4'>
            <TabsTrigger value='course' className='flex items-center space-x-2'>
              <BookOpen className='h-4 w-4' />
              <span>Course Content</span>
            </TabsTrigger>
            <TabsTrigger
              value='progress'
              className='flex items-center space-x-2'
            >
              <BarChart3 className='h-4 w-4' />
              <span>Progress</span>
            </TabsTrigger>
            <TabsTrigger value='exam' className='flex items-center space-x-2'>
              <Trophy className='h-4 w-4' />
              <span>Final Exam</span>
            </TabsTrigger>
            <TabsTrigger
              value='certificate'
              className='flex items-center space-x-2'
            >
              <Award className='h-4 w-4' />
              <span>Certificate</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value='course' className='mt-4'>
            <CourseTab
              courseData={courseData}
              expandedModules={expandedModules}
              expandedChapters={expandedChapters}
              expandedContents={expandedContents}
              onToggleModule={toggleModule}
              onToggleChapter={toggleChapter}
              onToggleContent={toggleContent}
              onToggleContentComplete={toggleContentComplete}
              onStartQuiz={startQuiz}
              onNavigateToSection={handleNavigateToSection}
              onExpandAllModules={expandAllModules}
              onCollapseAllModules={collapseAllModules}
              onExpandAllChaptersInModule={expandAllChaptersInModule}
              onCollapseAllChaptersInModule={collapseAllChaptersInModule}
            />
          </TabsContent>

          <TabsContent value='progress' className='mt-4'>
            <ProgressTab
              courseData={courseData}
              overallProgress={overallProgress}
            />
          </TabsContent>

          <TabsContent value='exam' className='mt-4'>
            <ExamTab courseData={courseData} onStartQuiz={startQuiz} />
          </TabsContent>

          <TabsContent value='certificate' className='mt-4'>
            <CertificateTab
              courseData={courseData}
              institution={{
                id: 'iai-indonesia',
                name: 'Indonesian Institute of Architects',
                shortName: 'IAI',
                website: 'https://iai.or.id',
                certificateTemplate: {
                  primaryColor: '#1e40af',
                  secondaryColor: '#f59e0b',
                  signatoryName: 'Prof. Dr. Ir. Ahmad Djuhara',
                  signatoryTitle: 'Chairman, Indonesian Institute of Architects'
                }
              }}
              overallProgress={overallProgress}
              onGenerateCertificate={generateCertificate}
              onShowCertificate={() => setShowCertificate(true)}
            />
          </TabsContent>
        </Tabs>

        {/* Quiz Modal */}
        {currentQuiz && (
          <QuizModal
            quiz={currentQuiz}
            isOpen={true}
            onComplete={handleQuizComplete}
            onClose={() => setCurrentQuiz(null)}
          />
        )}

        {/* Certificate Modal */}
        <Dialog open={showCertificate} onOpenChange={setShowCertificate}>
          <DialogContent className='max-h-[90vh] max-w-4xl overflow-y-auto'>
            <DialogHeader>
              <DialogTitle className='flex items-center space-x-2'>
                <Award className='h-5 w-5' />
                <span>Your Certificate</span>
              </DialogTitle>
            </DialogHeader>
            <div className='mt-6'>
              <CertificateTemplate
                course={courseData}
                institution={{
                  id: 'iai-indonesia',
                  name: 'Indonesian Institute of Architects',
                  shortName: 'IAI',
                  website: 'https://iai.or.id',
                  certificateTemplate: {
                    primaryColor: '#1e40af',
                    secondaryColor: '#f59e0b',
                    signatoryName: 'Prof. Dr. Ir. Ahmad Djuhara',
                    signatoryTitle:
                      'Chairman, Indonesian Institute of Architects'
                  }
                }}
                studentName='John Doe'
                completionDate={
                  courseData.certificate.completionDate ||
                  new Date().toISOString().split('T')[0]
                }
              />
            </div>
            <div className='sticky bottom-0 mt-6 flex justify-end space-x-2 border-t bg-white pt-4'>
              <Button
                variant='outline'
                onClick={() => setShowCertificate(false)}
              >
                Close
              </Button>
              <Button onClick={() => window.print()}>
                <Download className='mr-2 h-4 w-4' />
                Download PDF
              </Button>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
};

export default CoursePage;
