import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { classes, users, courseEnrollments, courses } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/classes/[id] - Get a specific class
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const classId = parseInt(params.id);
    
    if (isNaN(classId)) {
      return NextResponse.json({ error: 'Invalid class ID' }, { status: 400 });
    }

    // Get the class with teacher information
    const classData = await db
      .select({
        id: classes.id,
        name: classes.name,
        description: classes.description,
        institutionId: classes.institutionId,
        teacherId: classes.teacherId,
        coverPicture: classes.coverPicture,
        createdAt: classes.createdAt,
        updatedAt: classes.updatedAt,
        teacherName: users.name,
        teacherEmail: users.email
      })
      .from(classes)
      .leftJoin(users, eq(classes.teacherId, users.id))
      .where(eq(classes.id, classId))
      .limit(1);

    if (classData.length === 0) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 });
    }

    // Get enrolled courses for this class
    const enrolledCourses = await db
      .select({
        courseId: courses.id,
        courseName: courses.name,
        courseDescription: courses.description,
        courseCode: courses.courseCode,
        courseType: courses.type,
        enrolledAt: courseEnrollments.enrolledAt
      })
      .from(courseEnrollments)
      .leftJoin(courses, eq(courseEnrollments.courseId, courses.id))
      .where(eq(courseEnrollments.classId, classId));

    const classWithDetails = {
      ...classData[0],
      enrolledCourses,
      studentCount: 0, // TODO: Calculate actual student count
      courseCount: enrolledCourses.length
    };

    return NextResponse.json({ class: classWithDetails });
  } catch (error) {
    console.error('Error fetching class:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/classes/[id] - Update a specific class
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const classId = parseInt(params.id);
    const body = await request.json();
    const { name, description, coverPicture, teacherId } = body;

    if (isNaN(classId)) {
      return NextResponse.json({ error: 'Invalid class ID' }, { status: 400 });
    }

    // Check if class exists
    const existingClass = await db
      .select()
      .from(classes)
      .where(eq(classes.id, classId))
      .limit(1);

    if (existingClass.length === 0) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 });
    }

    // If teacherId is being updated, verify the new teacher
    if (teacherId && teacherId !== existingClass[0].teacherId) {
      const teacher = await db
        .select()
        .from(users)
        .where(
          and(
            eq(users.id, teacherId),
            eq(users.institutionId, existingClass[0].institutionId),
            eq(users.role, 'teacher')
          )
        )
        .limit(1);

      if (teacher.length === 0) {
        return NextResponse.json(
          { error: 'Teacher not found or not authorized' },
          { status: 403 }
        );
      }
    }

    // Update the class
    const updatedClass = await db
      .update(classes)
      .set({
        name: name || existingClass[0].name,
        description: description || existingClass[0].description,
        coverPicture: coverPicture || existingClass[0].coverPicture,
        teacherId: teacherId || existingClass[0].teacherId,
        updatedAt: new Date()
      })
      .where(eq(classes.id, classId))
      .returning();

    return NextResponse.json({
      class: updatedClass[0],
      message: 'Class updated successfully'
    });
  } catch (error) {
    console.error('Error updating class:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/classes/[id] - Delete a specific class
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const classId = parseInt(params.id);

    if (isNaN(classId)) {
      return NextResponse.json({ error: 'Invalid class ID' }, { status: 400 });
    }

    // Check if class exists
    const existingClass = await db
      .select()
      .from(classes)
      .where(eq(classes.id, classId))
      .limit(1);

    if (existingClass.length === 0) {
      return NextResponse.json({ error: 'Class not found' }, { status: 404 });
    }

    // TODO: Check if class has enrolled students or courses
    // For now, we'll allow deletion

    // Delete course enrollments first (foreign key constraint)
    await db
      .delete(courseEnrollments)
      .where(eq(courseEnrollments.classId, classId));

    // Delete the class
    await db
      .delete(classes)
      .where(eq(classes.id, classId));

    return NextResponse.json({
      message: 'Class deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting class:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}