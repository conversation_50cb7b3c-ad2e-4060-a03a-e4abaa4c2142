import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { chapters, modules, courses, quizzes } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/chapters/[id] - Get a specific chapter with quizzes
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const chapterId = parseInt(params.id);
    
    if (isNaN(chapterId)) {
      return NextResponse.json({ error: 'Invalid chapter ID' }, { status: 400 });
    }

    // Get chapter
    const chapterData = await db
      .select()
      .from(chapters)
      .where(eq(chapters.id, chapterId))
      .limit(1);

    if (chapterData.length === 0) {
      return NextResponse.json({ error: 'Chapter not found' }, { status: 404 });
    }

    const chapter = chapterData[0];

    // Get quizzes for this chapter
    const chapterQuizzes = await db
      .select()
      .from(quizzes)
      .where(eq(quizzes.chapterId, chapterId));

    return NextResponse.json({
      chapter: {
        ...chapter,
        quizzes: chapterQuizzes
      }
    });
  } catch (error) {
    console.error('Error fetching chapter:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/chapters/[id] - Update a chapter
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const chapterId = parseInt(params.id);
    
    if (isNaN(chapterId)) {
      return NextResponse.json({ error: 'Invalid chapter ID' }, { status: 400 });
    }

    const body = await request.json();
    const {
      title,
      content,
      orderIndex,
      videoUrl,
      duration,
      teacherId
    } = body;

    // Check if chapter exists
    const existingChapter = await db
      .select()
      .from(chapters)
      .where(eq(chapters.id, chapterId))
      .limit(1);

    if (existingChapter.length === 0) {
      return NextResponse.json({ error: 'Chapter not found' }, { status: 404 });
    }

    // Verify teacher has permission to update this chapter
    if (teacherId) {
      const chapterWithCourse = await db
        .select({
          chapterId: chapters.id,
          moduleId: chapters.moduleId,
          courseId: modules.courseId,
          teacherId: courses.teacherId
        })
        .from(chapters)
        .leftJoin(modules, eq(chapters.moduleId, modules.id))
        .leftJoin(courses, eq(modules.courseId, courses.id))
        .where(
          and(
            eq(chapters.id, chapterId),
            eq(courses.teacherId, teacherId)
          )
        )
        .limit(1);

      if (chapterWithCourse.length === 0) {
        return NextResponse.json(
          { error: 'Not authorized to update this chapter' },
          { status: 403 }
        );
      }
    }

    // Update the chapter
    const updatedChapter = await db
      .update(chapters)
      .set({
        ...(title && { title }),
        ...(content && { content }),
        ...(orderIndex !== undefined && { orderIndex }),
        ...(videoUrl && { videoUrl }),
        ...(duration !== undefined && { duration }),
        updatedAt: new Date()
      })
      .where(eq(chapters.id, chapterId))
      .returning();

    return NextResponse.json({
      chapter: updatedChapter[0],
      message: 'Chapter updated successfully'
    });
  } catch (error) {
    console.error('Error updating chapter:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/chapters/[id] - Delete a chapter
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const chapterId = parseInt(params.id);
    
    if (isNaN(chapterId)) {
      return NextResponse.json({ error: 'Invalid chapter ID' }, { status: 400 });
    }

    const searchParams = request.nextUrl.searchParams;
    const teacherId = searchParams.get('teacherId');

    // Check if chapter exists
    const existingChapter = await db
      .select()
      .from(chapters)
      .where(eq(chapters.id, chapterId))
      .limit(1);

    if (existingChapter.length === 0) {
      return NextResponse.json({ error: 'Chapter not found' }, { status: 404 });
    }

    // Verify teacher has permission to delete this chapter
    if (teacherId) {
      const chapterWithCourse = await db
        .select({
          chapterId: chapters.id,
          moduleId: chapters.moduleId,
          courseId: modules.courseId,
          teacherId: courses.teacherId
        })
        .from(chapters)
        .leftJoin(modules, eq(chapters.moduleId, modules.id))
        .leftJoin(courses, eq(modules.courseId, courses.id))
        .where(
          and(
            eq(chapters.id, chapterId),
            eq(courses.teacherId, parseInt(teacherId))
          )
        )
        .limit(1);

      if (chapterWithCourse.length === 0) {
        return NextResponse.json(
          { error: 'Not authorized to delete this chapter' },
          { status: 403 }
        );
      }
    }

    // Delete related data in correct order
    // 1. Delete quizzes first
    await db.delete(quizzes).where(eq(quizzes.chapterId, chapterId));

    // 2. Delete the chapter
    await db.delete(chapters).where(eq(chapters.id, chapterId));

    return NextResponse.json({ message: 'Chapter deleted successfully' });
  } catch (error) {
    console.error('Error deleting chapter:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}