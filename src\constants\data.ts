import { NavItem } from '@/types';

export type Product = {
  photo_url: string;
  name: string;
  description: string;
  created_at: string;
  price: number;
  id: number;
  category: string;
  updated_at: string;
};

//Info: The following data is used for the sidebar navigation and Cmd K bar.
export const getNavItems = (pathname: string): NavItem[] => [
  {
    title: 'Dashboard',
    url: '/dashboard/overview',
    icon: 'dashboard',
    isActive: pathname === '/dashboard/overview',
    shortcut: ['d', 'd'],
    items: [] // Empty array as there are no child items for Dashboard
  },
  {
    title: 'Learning',
    url: '#', // Placeholder as there is no direct link for the parent
    icon: 'school',
    isActive: pathname.startsWith('/enroll') || pathname.startsWith('/modules'),
    items: [
      {
        title: 'Enroll Course',
        url: '/enroll',
        icon: 'enrollment',
        shortcut: ['e', 'e']
      },
      {
        title: 'My Modules',
        url: '/modules',
        icon: 'book',
        shortcut: ['m', 'o']
      }
    ]
  }
];

// Legacy export for backward compatibility
export const navItems: NavItem[] = getNavItems('');

export interface SaleUser {
  id: number;
  name: string;
  email: string;
  amount: string;
  image: string;
  initials: string;
}

export const recentSalesData: SaleUser[] = [
  {
    id: 1,
    name: 'Olivia Martin',
    email: '<EMAIL>',
    amount: '+$1,999.00',
    image: 'https://api.slingacademy.com/public/sample-users/1.png',
    initials: 'OM'
  },
  {
    id: 2,
    name: 'Jackson Lee',
    email: '<EMAIL>',
    amount: '+$39.00',
    image: 'https://api.slingacademy.com/public/sample-users/2.png',
    initials: 'JL'
  },
  {
    id: 3,
    name: 'Isabella Nguyen',
    email: '<EMAIL>',
    amount: '+$299.00',
    image: 'https://api.slingacademy.com/public/sample-users/3.png',
    initials: 'IN'
  },
  {
    id: 4,
    name: 'William Kim',
    email: '<EMAIL>',
    amount: '+$99.00',
    image: 'https://api.slingacademy.com/public/sample-users/4.png',
    initials: 'WK'
  },
  {
    id: 5,
    name: 'Sofia Davis',
    email: '<EMAIL>',
    amount: '+$39.00',
    image: 'https://api.slingacademy.com/public/sample-users/5.png',
    initials: 'SD'
  }
];
