'use client';

import { useState, useEffect } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { Skeleton } from '@/components/ui/skeleton';
import {
  FileText,
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Clock,
  Loader2
} from 'lucide-react';
import Link from 'next/link';
import { toast } from 'sonner';
import { authStorage } from '@/lib/auth';

interface Quiz {
  id: number;
  title: string;
  description: string;
  type: string;
  timeLimit: number;
  maxAttempts: number;
  passingScore: number;
  isActive: boolean;
  chapterId: number;
  createdAt: string;
  updatedAt: string;
  chapterTitle: string;
  moduleName: string;
  courseName: string;
  courseId: number;
  questionCount: number;
}

export default function QuizzesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [quizzes, setQuizzes] = useState<Quiz[]>([]);
  const [loading, setLoading] = useState(true);
  const [deleting, setDeleting] = useState<number | null>(null);

  const fetchQuizzes = async () => {
    try {
      setLoading(true);
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to view quizzes');
        return;
      }

      const response = await fetch(`/api/quizzes?teacherId=${user.id}`);
      if (!response.ok) {
        throw new Error('Failed to fetch quizzes');
      }

      const data = await response.json();
      setQuizzes(data.quizzes || []);
    } catch (error) {
      console.error('Error fetching quizzes:', error);
      toast.error('Failed to load quizzes');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteQuiz = async (quizId: number) => {
    try {
      setDeleting(quizId);
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to delete quizzes');
        return;
      }

      const response = await fetch(`/api/quizzes/${quizId}?teacherId=${user.id}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to delete quiz');
      }

      toast.success('Quiz deleted successfully');
      fetchQuizzes(); // Refresh the list
    } catch (error) {
      console.error('Error deleting quiz:', error);
      toast.error('Failed to delete quiz');
    } finally {
      setDeleting(null);
    }
  };

  useEffect(() => {
    fetchQuizzes();
  }, []);

  const filteredQuizzes = quizzes.filter(
    (quiz) =>
      quiz.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      quiz.courseName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      quiz.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      quiz.chapterTitle.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>
            Quizzes & Assessments
          </h1>
          <p className='text-muted-foreground'>
            Create and manage quizzes for your courses
          </p>
        </div>
        <Link href='/dashboard/teacher/quizzes/new'>
          <Button>
            <Plus className='mr-2 h-4 w-4' />
            Create Quiz
          </Button>
        </Link>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Quizzes</CardTitle>
          <CardDescription>
            View and manage all your quizzes and assessments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='mb-4 flex items-center space-x-2'>
            <div className='relative flex-1'>
              <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
              <Input
                placeholder='Search quizzes...'
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className='pl-8'
              />
            </div>
          </div>

          <div className='rounded-md border'>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Quiz</TableHead>
                  <TableHead>Course/Chapter</TableHead>
                  <TableHead>Questions</TableHead>
                  <TableHead>Time Limit</TableHead>
                  <TableHead>Performance</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className='w-[70px]'>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  // Loading skeleton
                  Array.from({ length: 3 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <div className='space-y-2'>
                          <Skeleton className='h-4 w-[200px]' />
                          <Skeleton className='h-3 w-[300px]' />
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='space-y-2'>
                          <Skeleton className='h-3 w-[150px]' />
                          <Skeleton className='h-3 w-[100px]' />
                        </div>
                      </TableCell>
                      <TableCell>
                        <Skeleton className='h-4 w-[40px]' />
                      </TableCell>
                      <TableCell>
                        <Skeleton className='h-4 w-[50px]' />
                      </TableCell>
                      <TableCell>
                        <div className='space-y-1'>
                          <Skeleton className='h-3 w-[80px]' />
                          <Skeleton className='h-3 w-[100px]' />
                        </div>
                      </TableCell>
                      <TableCell>
                        <Skeleton className='h-6 w-[70px]' />
                      </TableCell>
                      <TableCell>
                        <Skeleton className='h-8 w-8' />
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  filteredQuizzes.map((quiz) => (
                    <TableRow key={quiz.id}>
                      <TableCell>
                        <div className='space-y-1'>
                          <p className='font-medium'>{quiz.title}</p>
                          <p className='text-muted-foreground text-sm'>
                            {quiz.description}
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='space-y-1'>
                          <p className='text-sm font-medium'>{quiz.courseName}</p>
                          {quiz.chapterTitle && (
                            <p className='text-muted-foreground text-xs'>
                              {quiz.chapterTitle}
                            </p>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='flex items-center space-x-1'>
                          <FileText className='text-muted-foreground h-4 w-4' />
                          <span>{quiz.questionCount}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='flex items-center space-x-1'>
                          <Clock className='text-muted-foreground h-4 w-4' />
                          <span>{quiz.timeLimit || 'No limit'}</span>
                          {quiz.timeLimit && <span>m</span>}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className='space-y-1'>
                          <p className='text-sm'>Max: {quiz.maxAttempts} attempts</p>
                          <p className='text-muted-foreground text-xs'>
                            Min: {quiz.passingScore}%
                          </p>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            quiz.isActive ? 'default' : 'outline'
                          }
                        >
                          {quiz.isActive ? 'published' : 'draft'}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button 
                              variant='ghost' 
                              className='h-8 w-8 p-0'
                              disabled={deleting === quiz.id}
                            >
                              {deleting === quiz.id ? (
                                <Loader2 className='h-4 w-4 animate-spin' />
                              ) : (
                                <MoreHorizontal className='h-4 w-4' />
                              )}
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align='end'>
                            <DropdownMenuItem asChild>
                              <Link
                                href={`/dashboard/teacher/quizzes/${quiz.id}/edit`}
                              >
                                <Edit className='mr-2 h-4 w-4' />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link
                                href={`/dashboard/teacher/quizzes/${quiz.id}/results`}
                              >
                                <Eye className='mr-2 h-4 w-4' />
                                View Results
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem asChild>
                              <Link
                                href={`/dashboard/teacher/quizzes/${quiz.id}/preview`}
                              >
                                <FileText className='mr-2 h-4 w-4' />
                                Preview
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              className='text-red-600'
                              onClick={() => handleDeleteQuiz(quiz.id)}
                              disabled={deleting === quiz.id}
                            >
                              <Trash2 className='mr-2 h-4 w-4' />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                     </TableRow>
                   ))
                 )}
              </TableBody>
            </Table>
          </div>

          {filteredQuizzes.length === 0 && (
            <div className='py-8 text-center'>
              <FileText className='text-muted-foreground mx-auto h-12 w-12' />
              <h3 className='mt-2 text-sm font-semibold'>No quizzes found</h3>
              <p className='text-muted-foreground mt-1 text-sm'>
                {searchTerm
                  ? 'Try adjusting your search terms.'
                  : 'Get started by creating a new quiz.'}
              </p>
              {!searchTerm && (
                <div className='mt-6'>
                  <Link href='/dashboard/teacher/quizzes/new'>
                    <Button>
                      <Plus className='mr-2 h-4 w-4' />
                      Create Quiz
                    </Button>
                  </Link>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
