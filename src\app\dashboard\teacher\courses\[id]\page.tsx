'use client';

import { useState, useEffect, use } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import {
  BookOpen,
  Users,
  FileText,
  Clock,
  Award,
  Edit,
  Eye,
  Play,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import Link from 'next/link';
import { authStorage } from '@/lib/auth';
import { toast } from 'sonner';

interface Chapter {
  id: number;
  name: string;
  description: string;
  hasQuiz: boolean;
  quizId?: number;
  progress: number;
}

interface Module {
  id: number;
  name: string;
  description: string;
  orderIndex: number;
  chapters: Chapter[];
  hasModuleQuiz: boolean;
  moduleQuizId?: number;
  progress: number;
}

interface Course {
  id: number;
  name: string;
  description: string;
  type: string;
  courseCode: string;
  status: string;
  studentCount: number;
  completionRate: number;
  modules: Module[];
  hasFinalExam: boolean;
  finalExamId?: number;
  overallProgress: number;
}

interface QuizStats {
  totalQuizzes: number;
  chapterQuizzes: number;
  moduleQuizzes: number;
  finalExam: number;
  averageScore: number;
  completionRate: number;
}

export default function CourseDetailPage({
  params
}: {
  params: Promise<{ id: string }>;
}) {
  // Unwrap the Promise using React's use() hook
  const { id } = use(params);
  
  const [course, setCourse] = useState<Course | null>(null);
  const [quizStats, setQuizStats] = useState<QuizStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (id) {
      fetchCourseData();
    }
  }, [id]);

  const fetchCourseData = async () => {
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to view course details');
        return;
      }

      const response = await fetch(`/api/courses/${id}?teacherId=${user.id}`);
      const data = await response.json();

      if (data.success && data.course) {
        setCourse(data.course);
        
        // Calculate quiz stats from course data
        const stats = calculateQuizStats(data.course);
        setQuizStats(stats);
      } else {
        toast.error(data.error || 'Failed to fetch course details');
      }
    } catch (error) {
      console.error('Error fetching course:', error);
      toast.error('Failed to fetch course details');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateQuizStats = (courseData: Course): QuizStats => {
    let chapterQuizzes = 0;
    let moduleQuizzes = 0;
    
    courseData.modules.forEach(module => {
      chapterQuizzes += module.chapters.filter(chapter => chapter.hasQuiz).length;
      if (module.hasModuleQuiz) moduleQuizzes++;
    });
    
    const finalExam = courseData.hasFinalExam ? 1 : 0;
    const totalQuizzes = chapterQuizzes + moduleQuizzes + finalExam;
    
    return {
      totalQuizzes,
      chapterQuizzes,
      moduleQuizzes,
      finalExam,
      averageScore: 0, // This would come from actual quiz results
      completionRate: courseData.completionRate
    };
  };

  if (isLoading) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-center'>
          <div className='animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto'></div>
          <p className='mt-2'>Loading course details...</p>
        </div>
      </div>
    );
  }

  if (!course || !quizStats) {
    return (
      <div className='flex items-center justify-center min-h-screen'>
        <div className='text-center'>
          <h2 className='text-2xl font-bold'>Course not found</h2>
          <p className='text-muted-foreground mt-2'>The course you're looking for doesn't exist or you don't have permission to view it.</p>
        </div>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {/* Course Header */}
      <div className='flex items-start justify-between'>
        <div className='space-y-2'>
          <div className='flex items-center space-x-2'>
            <h1 className='text-3xl font-bold tracking-tight'>{course.name}</h1>
            <Badge
              variant={course.status === 'published' ? 'default' : 'outline'}
            >
              {course.status}
            </Badge>
          </div>
          <p className='text-muted-foreground'>{course.description}</p>
          <div className='text-muted-foreground flex items-center space-x-4 text-sm'>
            <span>Code: {course.courseCode}</span>
            <span>•</span>
            <span>{course.studentCount} students</span>
            <span>•</span>
            <span>{course.completionRate}% completion rate</span>
          </div>
        </div>
        <div className='flex space-x-2'>
          <Button variant='outline'>
            <Edit className='mr-2 h-4 w-4' />
            Edit Course
          </Button>
          <Button>
            <Eye className='mr-2 h-4 w-4' />
            Preview
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Total Quizzes</CardTitle>
            <FileText className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{quizStats.totalQuizzes}</div>
            <p className='text-muted-foreground text-xs'>
              {quizStats.chapterQuizzes} chapter + {quizStats.moduleQuizzes}{' '}
              module + {quizStats.finalExam} final
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Students</CardTitle>
            <Users className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{course.studentCount}</div>
            <p className='text-muted-foreground text-xs'>Enrolled students</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Average Score</CardTitle>
            <Award className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{quizStats.averageScore}%</div>
            <p className='text-muted-foreground text-xs'>Across all quizzes</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>Completion</CardTitle>
            <CheckCircle className='text-muted-foreground h-4 w-4' />
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{course.completionRate}%</div>
            <p className='text-muted-foreground text-xs'>Overall progress</p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue='structure' className='space-y-6'>
        <TabsList>
          <TabsTrigger value='structure'>Course Structure</TabsTrigger>
          <TabsTrigger value='quizzes'>Quiz Management</TabsTrigger>
          <TabsTrigger value='students'>Student Progress</TabsTrigger>
          <TabsTrigger value='analytics'>Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value='structure'>
          <div className='space-y-6'>
            {course.modules.map((module) => (
              <Card key={module.id}>
                <CardHeader>
                  <div className='flex items-center justify-between'>
                    <div>
                      <CardTitle className='flex items-center space-x-2'>
                        <span>
                          Module {module.orderIndex}: {module.name}
                        </span>
                        {module.hasModuleQuiz && (
                          <Badge variant='secondary'>Module Quiz</Badge>
                        )}
                      </CardTitle>
                      <CardDescription>{module.description}</CardDescription>
                    </div>
                    <div className='text-right'>
                      <div className='text-2xl font-bold'>
                        {module.progress}%
                      </div>
                      <p className='text-muted-foreground text-sm'>Progress</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='space-y-4'>
                    <Progress value={module.progress} className='h-2' />

                    <div className='space-y-3'>
                      {module.chapters.map((chapter) => (
                        <div
                          key={chapter.id}
                          className='flex items-center justify-between rounded-lg border p-3'
                        >
                          <div className='flex items-center space-x-3'>
                            <div
                              className={`flex h-8 w-8 items-center justify-center rounded-full ${
                                chapter.progress === 100
                                  ? 'bg-green-100 text-green-600'
                                  : 'bg-gray-100 text-gray-600'
                              }`}
                            >
                              {chapter.progress === 100 ? (
                                <CheckCircle className='h-4 w-4' />
                              ) : (
                                <BookOpen className='h-4 w-4' />
                              )}
                            </div>
                            <div>
                              <p className='font-medium'>{chapter.name}</p>
                              <p className='text-muted-foreground text-sm'>
                                {chapter.description}
                              </p>
                            </div>
                          </div>
                          <div className='flex items-center space-x-2'>
                            <div className='mr-4 text-right'>
                              <p className='text-sm font-medium'>
                                {chapter.progress}%
                              </p>
                              <Progress
                                value={chapter.progress}
                                className='h-1 w-16'
                              />
                            </div>
                            {chapter.hasQuiz && (
                              <Link
                                href={`/dashboard/teacher/quizzes/${chapter.quizId}`}
                              >
                                <Button variant='outline' size='sm'>
                                  <FileText className='mr-1 h-3 w-3' />
                                  Quiz
                                </Button>
                              </Link>
                            )}
                            <Button variant='outline' size='sm'>
                              <Edit className='mr-1 h-3 w-3' />
                              Edit
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>

                    {module.hasModuleQuiz && (
                      <div className='mt-4 rounded-lg border border-blue-200 bg-blue-50 p-3'>
                        <div className='flex items-center justify-between'>
                          <div className='flex items-center space-x-2'>
                            <FileText className='h-4 w-4 text-blue-600' />
                            <span className='font-medium text-blue-900'>
                              Module Quiz: {module.name}
                            </span>
                          </div>
                          <Link
                            href={`/dashboard/teacher/quizzes/${module.moduleQuizId}`}
                          >
                            <Button variant='outline' size='sm'>
                              <Eye className='mr-1 h-3 w-3' />
                              View Quiz
                            </Button>
                          </Link>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}

            {course.hasFinalExam && (
              <Card className='border-2 border-dashed border-yellow-300 bg-yellow-50'>
                <CardContent className='pt-6'>
                  <div className='flex items-center justify-between'>
                    <div className='flex items-center space-x-3'>
                      <div className='flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100'>
                        <Award className='h-6 w-6 text-yellow-600' />
                      </div>
                      <div>
                        <h3 className='font-semibold text-yellow-900'>
                          Final Examination
                        </h3>
                        <p className='text-sm text-yellow-700'>
                          Comprehensive exam covering all course materials
                        </p>
                      </div>
                    </div>
                    <Link
                      href={`/dashboard/teacher/quizzes/${course.finalExamId}`}
                    >
                      <Button>
                        <Eye className='mr-2 h-4 w-4' />
                        View Final Exam
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        <TabsContent value='quizzes'>
          <Card>
            <CardHeader>
              <CardTitle>Quiz Overview</CardTitle>
              <CardDescription>
                Manage all quizzes integrated within this course
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='py-8 text-center'>
                <FileText className='text-muted-foreground mx-auto h-12 w-12' />
                <h3 className='mt-2 text-sm font-semibold'>
                  Quiz management coming soon
                </h3>
                <p className='text-muted-foreground mt-1 text-sm'>
                  This will show all chapter quizzes, module quizzes, and final
                  exam in one place.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='students'>
          <Card>
            <CardHeader>
              <CardTitle>Student Progress</CardTitle>
              <CardDescription>
                Track student progress through modules and quizzes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='py-8 text-center'>
                <Users className='text-muted-foreground mx-auto h-12 w-12' />
                <h3 className='mt-2 text-sm font-semibold'>
                  Student tracking coming soon
                </h3>
                <p className='text-muted-foreground mt-1 text-sm'>
                  This will show detailed student progress through each module
                  and quiz.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value='analytics'>
          <Card>
            <CardHeader>
              <CardTitle>Course Analytics</CardTitle>
              <CardDescription>
                Detailed analytics for course performance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className='py-8 text-center'>
                <AlertCircle className='text-muted-foreground mx-auto h-12 w-12' />
                <h3 className='mt-2 text-sm font-semibold'>
                  Analytics coming soon
                </h3>
                <p className='text-muted-foreground mt-1 text-sm'>
                  This will show detailed analytics for quiz performance and
                  student engagement.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
