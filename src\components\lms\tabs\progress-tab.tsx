import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { BarChart3, Target } from 'lucide-react';
import { Course } from '@/types/lms';

interface ProgressTabProps {
  courseData: Course;
  overallProgress: number;
}

export const ProgressTab: React.FC<ProgressTabProps> = ({
  courseData,
  overallProgress
}) => {
  return (
    <div className='grid gap-6'>
      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle className='flex items-center space-x-2'>
            <BarChart3 className='h-5 w-5' />
            <span>Learning Progress Overview</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className='grid grid-cols-1 gap-6 md:grid-cols-3'>
            <div className='text-center'>
              <div className='mb-2 text-3xl font-bold text-blue-600'>
                {overallProgress}%
              </div>
              <p className='text-gray-600'>Overall Progress</p>
              <Progress value={overallProgress} className='mt-2' />
            </div>
            <div className='text-center'>
              <div className='mb-2 text-3xl font-bold text-green-600'>
                {courseData.modules.filter((m) => m.moduleQuiz.isPassed).length}
              </div>
              <p className='text-gray-600'>Modules Completed</p>
              <Progress
                value={
                  (courseData.modules.filter((m) => m.moduleQuiz.isPassed)
                    .length /
                    courseData.modules.length) *
                  100
                }
                className='mt-2'
              />
            </div>
            <div className='text-center'>
              <div className='mb-2 text-3xl font-bold text-purple-600'>0</div>
              <p className='text-gray-600'>Hours Studied</p>
              <p className='mt-1 text-sm text-gray-500'>
                Time tracking coming soon
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Module Progress Details */}
      <Card>
        <CardHeader>
          <CardTitle>Module Progress Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className='space-y-4'>
            {courseData.modules.map((module) => {
              const completedChapters = module.chapters.filter(
                (ch) =>
                  ch.contents.every((c) => c.isCompleted) && ch.quiz.isPassed
              ).length;
              const moduleProgress =
                (completedChapters / module.chapters.length) * 100;

              return (
                <div key={module.id} className='rounded-lg border p-4'>
                  <div className='mb-3 flex items-center justify-between'>
                    <h4 className='font-medium'>{module.title}</h4>
                    <Badge
                      variant={
                        module.moduleQuiz.isPassed ? 'default' : 'outline'
                      }
                    >
                      {module.moduleQuiz.isPassed ? 'Completed' : 'In Progress'}
                    </Badge>
                  </div>
                  <Progress value={moduleProgress} className='mb-2' />
                  <div className='flex justify-between text-sm text-gray-600'>
                    <span>
                      {completedChapters}/{module.chapters.length} chapters
                      completed
                    </span>
                    <span>{Math.round(moduleProgress)}%</span>
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Quiz Results */}
      <Card>
        <CardHeader>
          <CardTitle>Quiz Performance</CardTitle>
        </CardHeader>
        <CardContent>
          {(() => {
            const allQuizzes = [];

            // Collect all quizzes from chapters and modules
            courseData.modules.forEach((module) => {
              module.chapters.forEach((chapter) => {
                if (chapter.quiz.attempts > 0) {
                  allQuizzes.push({
                    ...chapter.quiz,
                    moduleName: module.title,
                    chapterName: chapter.title,
                    type: 'chapter' as const
                  });
                }
              });

              if (module.moduleQuiz.attempts > 0) {
                allQuizzes.push({
                  ...module.moduleQuiz,
                  moduleName: module.title,
                  chapterName: null,
                  type: 'module' as const
                });
              }
            });

            // Add final exam if attempted
            if (courseData.finalExam.attempts > 0) {
              allQuizzes.push({
                ...courseData.finalExam,
                moduleName: 'Final Assessment',
                chapterName: null,
                type: 'final' as const
              });
            }

            if (allQuizzes.length === 0) {
              return (
                <div className='py-8 text-center text-gray-500'>
                  <Target className='mx-auto mb-4 h-12 w-12 text-gray-400' />
                  <p>
                    Quiz results will appear here as you complete assessments
                  </p>
                </div>
              );
            }

            return (
              <div className='space-y-4'>
                {allQuizzes.map((quiz) => (
                  <div key={quiz.id} className='rounded-lg border p-4'>
                    <div className='mb-2 flex items-start justify-between'>
                      <div>
                        <h4 className='font-medium'>{quiz.title}</h4>
                        <p className='text-sm text-gray-600'>
                          {quiz.moduleName}
                          {quiz.chapterName ? ` • ${quiz.chapterName}` : ''}
                        </p>
                      </div>
                      <Badge
                        variant={quiz.isPassed ? 'default' : 'destructive'}
                      >
                        {quiz.isPassed ? 'Passed' : 'Failed'}
                      </Badge>
                    </div>

                    <div className='grid grid-cols-2 gap-4 text-sm md:grid-cols-4'>
                      <div>
                        <span className='text-gray-500'>Score:</span>
                        <div className='font-medium'>
                          {quiz.lastScore !== undefined
                            ? `${quiz.lastScore}%`
                            : 'N/A'}
                        </div>
                      </div>
                      <div>
                        <span className='text-gray-500'>Required:</span>
                        <div className='font-medium'>{quiz.minimumScore}%</div>
                      </div>
                      <div>
                        <span className='text-gray-500'>Attempts:</span>
                        <div className='font-medium'>
                          {quiz.attempts}/{quiz.maxAttempts}
                        </div>
                      </div>
                      <div>
                        <span className='text-gray-500'>Type:</span>
                        <div className='font-medium capitalize'>
                          {quiz.type}
                        </div>
                      </div>
                    </div>

                    {quiz.lastScore !== undefined && (
                      <div className='mt-3'>
                        <Progress
                          value={quiz.lastScore}
                          className={`h-2 ${quiz.isPassed ? 'text-green-600' : 'text-red-600'}`}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            );
          })()}
        </CardContent>
      </Card>
    </div>
  );
};
