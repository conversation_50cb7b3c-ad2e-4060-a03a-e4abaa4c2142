import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { questions, quizzes, chapters, modules, courses } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/questions - Get questions for a quiz
export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const quizId = searchParams.get('quizId');
    const teacherId = searchParams.get('teacherId');
    
    if (!quizId) {
      return NextResponse.json({ error: 'Quiz ID required' }, { status: 400 });
    }

    // Verify quiz exists and teacher has access
    if (teacherId) {
      const quizWithCourse = await db
        .select({
          quizId: quizzes.id,
          chapterId: quizzes.chapterId,
          teacherId: courses.teacherId
        })
        .from(quizzes)
        .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
        .leftJoin(modules, eq(chapters.moduleId, modules.id))
        .leftJoin(courses, eq(modules.courseId, courses.id))
        .where(
          and(
            eq(quizzes.id, parseInt(quizId)),
            eq(courses.teacherId, parseInt(teacherId))
          )
        )
        .limit(1);

      if (quizWithCourse.length === 0) {
        return NextResponse.json(
          { error: 'Quiz not found or access denied' },
          { status: 403 }
        );
      }
    }

    // Get questions for this quiz
    const quizQuestions = await db
      .select()
      .from(questions)
      .where(eq(questions.quizId, parseInt(quizId)));

    // Parse options for each question
    const questionsWithParsedOptions = quizQuestions.map(question => ({
      ...question,
      options: question.options ? JSON.parse(question.options) : null
    }));

    return NextResponse.json({ questions: questionsWithParsedOptions });
  } catch (error) {
    console.error('Error fetching questions:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// POST /api/questions - Create a new question
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      quizId,
      type = 'multiple_choice',
      question,
      options,
      correctAnswer,
      explanation,
      points = 1,
      orderIndex,
      teacherId
    } = body;

    // Validate required fields
    if (!quizId || !question || !correctAnswer) {
      return NextResponse.json(
        { error: 'Quiz ID, question, and correct answer are required' },
        { status: 400 }
      );
    }

    // Verify quiz exists and teacher has access
    const quizWithCourse = await db
      .select({
        quizId: quizzes.id,
        chapterId: quizzes.chapterId,
        teacherId: courses.teacherId
      })
      .from(quizzes)
      .leftJoin(chapters, eq(quizzes.chapterId, chapters.id))
      .leftJoin(modules, eq(chapters.moduleId, modules.id))
      .leftJoin(courses, eq(modules.courseId, courses.id))
      .where(eq(quizzes.id, quizId))
      .limit(1);

    if (quizWithCourse.length === 0) {
      return NextResponse.json({ error: 'Quiz not found' }, { status: 404 });
    }

    if (teacherId && quizWithCourse[0].teacherId !== teacherId) {
      return NextResponse.json(
        { error: 'Not authorized to add questions to this quiz' },
        { status: 403 }
      );
    }

    // If no orderIndex provided, set it to the next available
    let finalOrderIndex = orderIndex;
    if (finalOrderIndex === undefined) {
      const existingQuestions = await db
        .select({ orderIndex: questions.orderIndex })
        .from(questions)
        .where(eq(questions.quizId, quizId));
      
      finalOrderIndex = existingQuestions.length > 0 
        ? Math.max(...existingQuestions.map(q => q.orderIndex || 0)) + 1 
        : 1;
    }

    // Create the question
    const newQuestion = await db
      .insert(questions)
      .values({
        quizId,
        type,
        question,
        options: options ? JSON.stringify(options) : null,
        correctAnswer,
        explanation,
        points,
        orderIndex: finalOrderIndex
      })
      .returning();

    // Parse options for response
    const questionWithParsedOptions = {
      ...newQuestion[0],
      options: newQuestion[0].options ? JSON.parse(newQuestion[0].options) : null
    };

    return NextResponse.json(
      { question: questionWithParsedOptions, message: 'Question created successfully' },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating question:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}