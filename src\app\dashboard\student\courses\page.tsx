'use client';

import { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { authStorage } from '@/lib/auth-storage';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  BookOpen,
  Search,
  Play,
  CheckCircle,
  Clock,
  Award
} from 'lucide-react';
import Link from 'next/link';

interface Course {
  id: number;
  name: string;
  description: string;
  type: string;
  courseCode: string;
  progress?: number;
  totalModules?: number;
  completedModules?: number;
  nextChapter?: string | null;
  dueDate?: string;
  status?: string;
  instructor?: string;
  duration?: string;
  difficulty?: string;
}

export default function StudentCoursesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [enrollmentCode, setEnrollmentCode] = useState('');
  const [enrolledCourses, setEnrolledCourses] = useState<Course[]>([]);
  const [availableCourses, setAvailableCourses] = useState<Course[]>([]);
  const [loading, setLoading] = useState(true);
  const [enrolling, setEnrolling] = useState(false);

  useEffect(() => {
    fetchCourses();
  }, []);

  const fetchCourses = async () => {
    try {
      const user = authStorage.getUser();
      if (!user) {
        toast.error('Please log in to view courses');
        return;
      }

      // Fetch enrolled courses
      const enrolledResponse = await fetch(`/api/enrollments?studentId=${user.id}`);
      if (enrolledResponse.ok) {
        const enrolledData = await enrolledResponse.json();
        setEnrolledCourses(enrolledData);
      }

      // Fetch available courses
      const availableResponse = await fetch('/api/courses');
      if (availableResponse.ok) {
        const availableData = await availableResponse.json();
        setAvailableCourses(availableData);
      }
    } catch (error) {
      console.error('Error fetching courses:', error);
      toast.error('Failed to load courses');
    } finally {
      setLoading(false);
    }
  };

  const handleEnrollWithCode = async () => {
    if (!enrollmentCode.trim()) return;

    const user = authStorage.getUser();
    if (!user) {
      toast.error('Please log in to enroll in courses');
      return;
    }

    setEnrolling(true);
    try {
      const response = await fetch('/api/enrollments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          studentId: user.id,
          courseCode: enrollmentCode.trim()
        }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(`Successfully enrolled in ${result.courseName}!`);
        setEnrollmentCode('');
        fetchCourses(); // Refresh the courses list
      } else {
        const error = await response.json();
        toast.error(error.message || 'Failed to enroll in course');
      }
    } catch (error) {
      console.error('Enrollment error:', error);
      toast.error('An error occurred while enrolling');
    } finally {
      setEnrolling(false);
    }
  };

  const filteredEnrolledCourses = enrolledCourses.filter(
    (course) =>
      course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredAvailableCourses = availableCourses.filter(
    (course) =>
      course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div>
          <h1 className='text-3xl font-bold tracking-tight'>My Courses</h1>
          <p className='text-muted-foreground'>
            Access your enrolled courses and discover new ones
          </p>
        </div>
      </div>

      {/* Quick Enrollment */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Enrollment</CardTitle>
          <CardDescription>
            Enter a course code to quickly enroll in a course
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className='flex space-x-2'>
            <Input
              placeholder='Enter course code (e.g., MATH101)'
              value={enrollmentCode}
              onChange={(e) => setEnrollmentCode(e.target.value.toUpperCase())}
              className='flex-1'
            />
            <Button
              onClick={handleEnrollWithCode}
              disabled={!enrollmentCode.trim() || enrolling}
            >
              {enrolling ? 'Enrolling...' : 'Enroll'}
            </Button>
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue='enrolled' className='space-y-6'>
        <TabsList>
          <TabsTrigger value='enrolled'>My Courses</TabsTrigger>
          <TabsTrigger value='available'>Available Courses</TabsTrigger>
        </TabsList>

        <div className='flex items-center space-x-2'>
          <div className='relative flex-1'>
            <Search className='text-muted-foreground absolute top-2.5 left-2 h-4 w-4' />
            <Input
              placeholder='Search courses...'
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className='pl-8'
            />
          </div>
        </div>

        <TabsContent value='enrolled'>
          {loading ? (
            <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
              {[1, 2, 3].map((i) => (
                <Card key={i} className='flex flex-col'>
                  <CardHeader>
                    <div className='space-y-2'>
                      <div className='bg-muted h-4 w-3/4 rounded animate-pulse'></div>
                      <div className='bg-muted h-3 w-1/2 rounded animate-pulse'></div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-2'>
                      <div className='bg-muted h-3 w-full rounded animate-pulse'></div>
                      <div className='bg-muted h-3 w-2/3 rounded animate-pulse'></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
            {filteredEnrolledCourses.map((course) => (
              <Card key={course.id} className='flex flex-col'>
                <CardHeader>
                  <div className='flex items-start justify-between'>
                    <div className='space-y-1'>
                      <CardTitle className='text-lg'>{course.name}</CardTitle>
                      <code className='bg-muted rounded px-2 py-1 text-sm'>
                        {course.courseCode}
                      </code>
                    </div>
                    <Badge
                      variant={
                        course.type === 'verified' ? 'default' : 'secondary'
                      }
                    >
                      {course.type}
                    </Badge>
                  </div>
                  <CardDescription>{course.description}</CardDescription>
                </CardHeader>
                <CardContent className='flex-1 space-y-4'>
                  <div className='space-y-2'>
                    <div className='flex items-center justify-between text-sm'>
                      <span>Progress</span>
                      <span>{course.progress}%</span>
                    </div>
                    <Progress value={course.progress} className='h-2' />
                    <p className='text-muted-foreground text-xs'>
                      {course.completedModules} of {course.totalModules} modules
                      completed
                    </p>
                  </div>

                  {course.status === 'in_progress' && course.nextChapter && (
                    <div className='space-y-2'>
                      <p className='text-sm font-medium'>Next:</p>
                      <p className='text-muted-foreground text-sm'>
                        {course.nextChapter}
                      </p>
                    </div>
                  )}

                  <div className='space-y-2'>
                    <div className='text-muted-foreground flex items-center space-x-2 text-sm'>
                      <Clock className='h-4 w-4' />
                      <span>
                        Due: {new Date(course.dueDate).toLocaleDateString()}
                      </span>
                    </div>
                    <Badge
                      variant={
                        course.status === 'completed' ? 'default' : 'outline'
                      }
                    >
                      {course.status === 'completed' ? (
                        <>
                          <CheckCircle className='mr-1 h-3 w-3' />
                          Completed
                        </>
                      ) : (
                        'In Progress'
                      )}
                    </Badge>
                  </div>
                </CardContent>
                <div className='p-6 pt-0'>
                  <Link href={`/dashboard/student/courses/${course.id}`}>
                    <Button className='w-full'>
                      {course.status === 'completed' ? (
                        <>
                          <Award className='mr-2 h-4 w-4' />
                          View Certificate
                        </>
                      ) : (
                        <>
                          <Play className='mr-2 h-4 w-4' />
                          Continue Learning
                        </>
                      )}
                    </Button>
                  </Link>
                </div>
              </Card>
            ))}
            </div>
          )}

          {!loading && filteredEnrolledCourses.length === 0 && (
            <Card>
              <CardContent className='pt-6'>
                <div className='py-8 text-center'>
                  <BookOpen className='text-muted-foreground mx-auto h-12 w-12' />
                  <h3 className='mt-2 text-sm font-semibold'>
                    No enrolled courses
                  </h3>
                  <p className='text-muted-foreground mt-1 text-sm'>
                    {searchTerm
                      ? 'No courses match your search.'
                      : 'Get started by enrolling in a course.'}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value='available'>
          {loading ? (
            <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
              {[1, 2, 3].map((i) => (
                <Card key={i} className='flex flex-col'>
                  <CardHeader>
                    <div className='space-y-2'>
                      <div className='bg-muted h-4 w-3/4 rounded animate-pulse'></div>
                      <div className='bg-muted h-3 w-1/2 rounded animate-pulse'></div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className='space-y-2'>
                      <div className='bg-muted h-3 w-full rounded animate-pulse'></div>
                      <div className='bg-muted h-3 w-2/3 rounded animate-pulse'></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
            {filteredAvailableCourses.map((course) => (
              <Card key={course.id} className='flex flex-col'>
                <CardHeader>
                  <div className='flex items-start justify-between'>
                    <div className='space-y-1'>
                      <CardTitle className='text-lg'>{course.name}</CardTitle>
                      <code className='bg-muted rounded px-2 py-1 text-sm'>
                        {course.courseCode}
                      </code>
                    </div>
                    <Badge
                      variant={
                        course.type === 'verified' ? 'default' : 'secondary'
                      }
                    >
                      {course.type}
                    </Badge>
                  </div>
                  <CardDescription>{course.description}</CardDescription>
                </CardHeader>
                <CardContent className='flex-1 space-y-4'>
                  <div className='space-y-2 text-sm'>
                    <div className='flex items-center justify-between'>
                      <span className='text-muted-foreground'>Instructor:</span>
                      <span>{course.instructor}</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-muted-foreground'>Duration:</span>
                      <span>{course.duration}</span>
                    </div>
                    <div className='flex items-center justify-between'>
                      <span className='text-muted-foreground'>Difficulty:</span>
                      <Badge variant='outline' className='text-xs'>
                        {course.difficulty}
                      </Badge>
                    </div>
                  </div>
                </CardContent>
                <div className='p-6 pt-0'>
                  <Button className='w-full' variant='outline'>
                    <BookOpen className='mr-2 h-4 w-4' />
                    Request Enrollment
                  </Button>
                </div>
              </Card>
            ))}
            </div>
          )}

          {!loading && filteredAvailableCourses.length === 0 && (
            <Card>
              <CardContent className='pt-6'>
                <div className='py-8 text-center'>
                  <BookOpen className='text-muted-foreground mx-auto h-12 w-12' />
                  <h3 className='mt-2 text-sm font-semibold'>
                    No available courses
                  </h3>
                  <p className='text-muted-foreground mt-1 text-sm'>
                    {searchTerm
                      ? 'No courses match your search.'
                      : 'Check back later for new courses.'}
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
