import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Book, Lock, ChevronUp, ChevronDown } from 'lucide-react';
import { ChapterSectionProps } from '@/types/lms';
import { ContentItem } from './content-item';
import { QuizCard } from './quiz-card';

export const ChapterSection: React.FC<ChapterSectionProps> = ({
  chapter,
  expandedContents,
  onToggleContent,
  onToggleContentComplete,
  onStartQuiz,
  isExpanded,
  onToggleExpanded
}) => {
  const completedContents = chapter.contents.filter(
    (c) => c.isCompleted
  ).length;
  const totalContents = chapter.contents.length;
  const progress =
    totalContents > 0 ? (completedContents / totalContents) * 100 : 0;

  return (
    <Card
      className={`my-3 border-l-4 ${chapter.isUnlocked ? 'border-l-green-400' : 'border-l-gray-300'} ${!chapter.isUnlocked ? 'opacity-60' : ''}`}
    >
      <CardContent className='p-4'>
        <div className='flex flex-col'>
          <div
            className='flex cursor-pointer items-center justify-between'
            onClick={() => chapter.isUnlocked && onToggleExpanded()}
          >
            <div className='flex flex-1 items-center space-x-3'>
              <div
                className={`rounded-lg p-2 ${chapter.isUnlocked ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-500'}`}
              >
                <Book className='h-5 w-5' />
              </div>
              <div className='flex-1'>
                <div className='flex items-center space-x-2'>
                  <span className='font-medium'>{chapter.title}</span>
                  {!chapter.isUnlocked && (
                    <Lock className='h-4 w-4 text-gray-400' />
                  )}
                </div>
                <div className='mt-2 max-w-md'>
                  <Progress value={progress} className='h-2' />
                </div>
                <p className='mt-1 text-sm text-gray-500'>
                  {completedContents}/{totalContents} contents completed
                  {chapter.quiz.isPassed && ' • Quiz passed'}
                </p>
              </div>
            </div>
            {chapter.isUnlocked &&
              (isExpanded ? (
                <ChevronUp className='h-5 w-5 text-gray-400' />
              ) : (
                <ChevronDown className='h-5 w-5 text-gray-400' />
              ))}
          </div>

          {isExpanded && chapter.isUnlocked && (
            <div className='mt-4 border-t pt-4'>
              {/* Chapter Contents */}
              <div className='space-y-2'>
                {chapter.contents.map((content) => (
                  <ContentItem
                    key={content.id}
                    content={content}
                    onToggleComplete={() => onToggleContentComplete(content.id)}
                    isExpanded={expandedContents[content.id] || false}
                    onToggleExpand={() => onToggleContent(content.id)}
                  />
                ))}
              </div>

              {/* Chapter Quiz */}
              <QuizCard
                quiz={chapter.quiz}
                isUnlocked={completedContents === totalContents}
                onStartQuiz={() => onStartQuiz(chapter.quiz.id)}
              />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
