import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { modules, courses, chapters, quizzes } from '@/lib/db/schema';
import { eq, and } from 'drizzle-orm';

// GET /api/modules/[id] - Get a specific module with chapters
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const moduleId = parseInt(params.id);
    
    if (isNaN(moduleId)) {
      return NextResponse.json({ error: 'Invalid module ID' }, { status: 400 });
    }

    // Get module
    const moduleData = await db
      .select()
      .from(modules)
      .where(eq(modules.id, moduleId))
      .limit(1);

    if (moduleData.length === 0) {
      return NextResponse.json({ error: 'Module not found' }, { status: 404 });
    }

    const module = moduleData[0];

    // Get chapters for this module
    const moduleChapters = await db
      .select()
      .from(chapters)
      .where(eq(chapters.moduleId, moduleId));

    // Get quizzes for each chapter
    const chaptersWithQuizzes = await Promise.all(
      moduleChapters.map(async (chapter) => {
        const chapterQuizzes = await db
          .select()
          .from(quizzes)
          .where(eq(quizzes.chapterId, chapter.id));

        return {
          ...chapter,
          quizzes: chapterQuizzes
        };
      })
    );

    return NextResponse.json({
      module: {
        ...module,
        chapters: chaptersWithQuizzes
      }
    });
  } catch (error) {
    console.error('Error fetching module:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// PUT /api/modules/[id] - Update a module
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const moduleId = parseInt(params.id);
    
    if (isNaN(moduleId)) {
      return NextResponse.json({ error: 'Invalid module ID' }, { status: 400 });
    }

    const body = await request.json();
    const {
      name,
      description,
      orderIndex,
      teacherId
    } = body;

    // Check if module exists
    const existingModule = await db
      .select()
      .from(modules)
      .where(eq(modules.id, moduleId))
      .limit(1);

    if (existingModule.length === 0) {
      return NextResponse.json({ error: 'Module not found' }, { status: 404 });
    }

    // Verify teacher has permission to update this module
    if (teacherId) {
      const course = await db
        .select()
        .from(courses)
        .where(
          and(
            eq(courses.id, existingModule[0].courseId),
            eq(courses.teacherId, teacherId)
          )
        )
        .limit(1);

      if (course.length === 0) {
        return NextResponse.json(
          { error: 'Not authorized to update this module' },
          { status: 403 }
        );
      }
    }

    // Update the module
    const updatedModule = await db
      .update(modules)
      .set({
        ...(name && { name }),
        ...(description && { description }),
        ...(orderIndex !== undefined && { orderIndex }),
        updatedAt: new Date()
      })
      .where(eq(modules.id, moduleId))
      .returning();

    return NextResponse.json({
      module: updatedModule[0],
      message: 'Module updated successfully'
    });
  } catch (error) {
    console.error('Error updating module:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// DELETE /api/modules/[id] - Delete a module
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const moduleId = parseInt(params.id);
    
    if (isNaN(moduleId)) {
      return NextResponse.json({ error: 'Invalid module ID' }, { status: 400 });
    }

    const searchParams = request.nextUrl.searchParams;
    const teacherId = searchParams.get('teacherId');

    // Check if module exists
    const existingModule = await db
      .select()
      .from(modules)
      .where(eq(modules.id, moduleId))
      .limit(1);

    if (existingModule.length === 0) {
      return NextResponse.json({ error: 'Module not found' }, { status: 404 });
    }

    // Verify teacher has permission to delete this module
    if (teacherId) {
      const course = await db
        .select()
        .from(courses)
        .where(
          and(
            eq(courses.id, existingModule[0].courseId),
            eq(courses.teacherId, parseInt(teacherId))
          )
        )
        .limit(1);

      if (course.length === 0) {
        return NextResponse.json(
          { error: 'Not authorized to delete this module' },
          { status: 403 }
        );
      }
    }

    // Delete related data in correct order
    // 1. Delete quizzes first
    const moduleChapters = await db
      .select({ id: chapters.id })
      .from(chapters)
      .where(eq(chapters.moduleId, moduleId));

    for (const chapter of moduleChapters) {
      await db.delete(quizzes).where(eq(quizzes.chapterId, chapter.id));
    }

    // 2. Delete chapters
    await db.delete(chapters).where(eq(chapters.moduleId, moduleId));

    // 3. Finally delete the module
    await db.delete(modules).where(eq(modules.id, moduleId));

    return NextResponse.json({ message: 'Module deleted successfully' });
  } catch (error) {
    console.error('Error deleting module:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}